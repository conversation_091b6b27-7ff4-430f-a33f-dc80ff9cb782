import { Component, OnInit, Inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { FormGroup, FormControl, Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { AuthService } from '../../core/services/auth.service';
import { catchError, finalize } from 'rxjs/operators';
import { throwError, EMPTY } from 'rxjs';
import { Router, RouterModule } from '@angular/router';
import { ActivatedRoute } from '@angular/router';
import { ToastService } from '../../core/services/toast.service';
import { NotifyType } from '../../constant/notify-type';
import { MfaStateService } from '../../core/services/mfa-state.service';
import { IconsModule } from '../../icons/icons.module';
import { AuthUtilsService } from '../../core/services/auth-utils.service';


@Component({
  selector: 'app-auth',
  standalone: true,
  imports: [
    FormsModule,
    CommonModule,
    TranslateModule,
    ReactiveFormsModule,
    RouterModule,
    IconsModule
  ],
  templateUrl: './auth.component.html',
  styleUrl: './auth.component.css'
})
export class AuthComponent implements OnInit {
  // Form definition with validation
  loginForm = new FormGroup({
    email: new FormControl('', [Validators.required]),
    password: new FormControl('', [Validators.required]),
    rememberMe: new FormControl(false)
  });

  // Component state
  isLoading = false;
  loginError = '';
  returnUrl = '/';

  constructor(
    private authService: AuthService,
    private route: ActivatedRoute,
    private toastService: ToastService,
    private mfaStateService: MfaStateService,
    private authUtils: AuthUtilsService,
    private router: Router
    
  ) {

      this.initFormWithSavedData();
    
  }

  ngOnInit(): void {
    // Get the returnUrl from query parameters
    this.route.queryParams.subscribe(params => {
      this.returnUrl = params['returnUrl'] || '/panel';
      console.log('Return URL from query params:', this.returnUrl);
    });

    // Check if user is already authenticated
 
      // Clear any invalid user data first
      const user = this.authUtils.getUserFromStorage();
      if (user && (!user.tokens || !user.tokens.access_token)) {
        console.log('Auth Component - Invalid user data found, clearing...');
        this.authUtils.clearAuthData();
      }

      // Check if user is properly authenticated
      if (this.authUtils.isAuthenticated()) {
        console.log('Auth Component - User is already authenticated, redirecting');
        const redirectUrl = localStorage.getItem('redirectAfterLogin') || '/panel';
        localStorage.removeItem('redirectAfterLogin');
        this.redirectTo(redirectUrl);
      }
    
  }

  // Getters for form controls (used in template)
  get email() {
    return this.loginForm.get('email') as FormControl;
  }

  get password() {
    return this.loginForm.get('password') as FormControl;
  }

  get rememberMe() {
    return this.loginForm.get('rememberMe') as FormControl;
  }

  /**
   * Handle form submission
   */
  onSubmit() {
    if (this.loginForm.status === 'VALID') {
      console.log('Login form submitted');
      this.isLoading = true;
      this.loginError = '';

      this.authService
        .login(this.loginForm.controls.email.value, this.loginForm.controls.password.value)
        .pipe(
          catchError(err => {
            console.error('Login error:', err);

            // Check if this is an MFA required error
            if (err && err.code === 400 &&
                err.message === 'Please enter the TOTP code to continue logging in' &&
                err.data && err.data.login_first_factor) {

              // Store username and password for MFA verification securely in memory
              const username = this.loginForm.controls.email.value || '';
              const password = this.loginForm.controls.password.value || '';
              this.mfaStateService.storeMfaInfo(
                username,
                password,
                err.data.login_first_factor,
                this.returnUrl || '/panel'
              );

              // Redirect to MFA page
              this.router.navigate(['/auth/mfa']);
              return EMPTY; // Don't propagate the error
            }

            // Handle other errors
            this.loginError = err.message || 'Login failed. Please check your credentials and try again.';
            // Show toast notification
            this.toastService.showToast(this.loginError, NotifyType.ERROR);
            return throwError(() => err);
          }),
          finalize(() => {
            this.isLoading = false;
          })
        )
        .subscribe({
          next: () => {
            console.log('Login successful');
            this.toastService.showToast('Login successful!', NotifyType.SUCCESS);

            // Only access localStorage in browser environment
         
              // Check if there's a stored redirect URL
              const redirectUrl = localStorage.getItem('redirectAfterLogin');
              console.log('Redirect URL from localStorage:', redirectUrl);

              if (redirectUrl) {
                console.log('Navigating to stored URL:', redirectUrl);
                localStorage.removeItem('redirectAfterLogin');
                this.redirectTo(redirectUrl);
                return;
              }
            

            // Default navigation if no redirect URL or not in browser
            console.log('Navigating to default return URL:', this.returnUrl);
            this.redirectTo(this.returnUrl || '/panel');
          },
          error: (error) => {
            console.error('Login subscription error:', error);
            // Error is already handled in the catchError operator
          }
        });
    } else {
      console.log('Login form is invalid');
      // Mark all form controls as touched to show validation errors
      for (const key in this.loginForm.controls) {
        // @ts-ignore
        const control = this.loginForm.controls[key];
        control.markAllAsTouched();
      }
    }
  }
  /**
   * Initialize form with saved data if available
   */
  private initFormWithSavedData() {

      const savedEmail = localStorage.getItem('userEmail');
      if (savedEmail) {
        this.loginForm.patchValue({
          email: savedEmail,
          rememberMe: true
        });
      }
    
  }

  private redirectTo(url: string) {
    window.location.href = url;
  }
}
