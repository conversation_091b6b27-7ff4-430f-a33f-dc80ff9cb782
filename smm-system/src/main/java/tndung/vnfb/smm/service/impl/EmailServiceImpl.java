package tndung.vnfb.smm.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import tndung.vnfb.smm.dto.connections.ConnectionSettingsDto;
import tndung.vnfb.smm.dto.connections.SmtpSettingsDto;
import tndung.vnfb.smm.entity.*;
import tndung.vnfb.smm.exception.IdErrorCode;
import tndung.vnfb.smm.exception.InvalidParameterException;
import tndung.vnfb.smm.service.ConnectionSettingsService;
import tndung.vnfb.smm.service.EmailService;
import tndung.vnfb.smm.service.EmailTemplateService;
import tndung.vnfb.smm.service.TenantService;

import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

/**
 * Service implementation for email operations
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EmailServiceImpl implements EmailService {

    private final ConnectionSettingsService connectionSettingsService;
    private final EmailTemplateService emailTemplateService;
    private final TenantService tenantService;

    @Override
    @Async
    public void sendOrderCompletionNotification(String tenantId, GOrder order, GUser user) {
        try {
            log.info("Sending order completion notification email for order: {} to user: {}",
                    order.getId(), user.getEmail());

            // Prepare template variables
            Map<String, String> variables = new HashMap<>();
            variables.put("username", user.getUserName());
            variables.put("email", user.getEmail());
            variables.put("order_id", order.getId().toString());
            variables.put("service_name", order.getService().getName());
            variables.put("quantity", order.getQuantity().toString());
            variables.put("amount", order.getCharge().toString());
            variables.put("link", order.getLink());
            variables.put("order_time", order.getCreatedAt().format(DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss")));

            // Add common variables
            //addCommonVariables(variables);

            // Send email using template
            sendTemplateEmail(tenantId, user.getEmail(), "order_completion", variables);

        } catch (Exception e) {
            log.error("Failed to send order completion notification email for order: {}, error: {}",
                    order.getId(), e.getMessage());
        }
    }

    @Override
    @Async
    public void sendNewOrderNotification(String tenantId, GOrder order, GUser user) {
        try {
            log.info("Sending new order notification email for order: {} to user: {}",
                    order.getId(), user.getEmail());



            // Prepare template variables
            Map<String, String> variables = new HashMap<>();
            variables.put("username", user.getUserName());
            variables.put("email", user.getEmail());
            variables.put("order_id", order.getId().toString());
            variables.put("service_name", order.getService().getName());
            variables.put("quantity", order.getQuantity().toString());
            variables.put("amount", order.getCharge().toString());
            variables.put("link", order.getLink());
            variables.put("order_time", order.getCreatedAt().format(DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss")));



            // Send email using template
            sendTemplateEmail(tenantId, user.getEmail(), "order-notification", variables);

        } catch (Exception e) {
            log.error("Failed to send new order notification email for order: {}, error: {}",
                    order.getId(), e.getMessage());
        }
    }

    @Override
    @Async
    public void sendTicketReplyNotification(String tenantId, Ticket ticket, Reply reply, GUser ticketCreator, GUser replyBy) {
        try {
            log.info("Sending ticket reply notification email for ticket: {} to user: {}",
                    ticket.getId(), ticketCreator.getEmail());

            // Prepare template variables
            Map<String, String> variables = new HashMap<>();
            variables.put("username", ticketCreator.getUserName());
            variables.put("email", ticketCreator.getEmail());
            variables.put("ticket_id", ticket.getId().toString());
            variables.put("subject", ticket.getSubject());
            variables.put("message", reply.getContent());
            variables.put("status", ticket.getStatus().toString());
            variables.put("created_time", ticket.getCreatedAt().format(DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss")));
            variables.put("reply_by", replyBy.getUserName());



            if (Boolean.FALSE.equals(ticketCreator.getVerified())) {
                log.error("Failed to send ticket reply notification email for user due to the user is not verified");
                return;
            }

            // Send email using template
            sendTemplateEmail(tenantId, ticketCreator.getEmail(), EmailTemplate.TemplateType.TICKET_REPLY_NOTIFICATION.getValue(), variables);

        } catch (Exception e) {
            log.error("Failed to send ticket reply notification email for ticket: {}, error: {}",
                    ticket.getId(), e.getMessage());
        }
    }

    @Override
    @Async
    public void sendTemplateEmail(String tenantId, String recipientEmail, String templateType, Map<String, String> variables) {
        try {



            // Get processed template
            Map<String, String> processedTemplate = emailTemplateService.getProcessedTemplate(tenantId, templateType, variables);

            // Check if template is disabled or not found
            if (processedTemplate == null) {
                log.warn("Template is disabled or not found for type: {}", templateType);
                return;
            }

            String subject = processedTemplate.get("subject");
            String content = processedTemplate.get("content");

            if (subject == null || content == null) {
                log.warn("Template not found or incomplete for type: {}", templateType);
                return;
            }

            // Send HTML email
            sendHtmlEmail(tenantId, recipientEmail, subject, content);

        } catch (Exception e) {
            log.error("Failed to send template email to: {}, template: {}, error: {}",
                    recipientEmail, templateType, e.getMessage());
        }
    }

    @Override
    public void sendHtmlEmail(String tenantId, String recipientEmail, String subject, String htmlContent) {
        try {
            JavaMailSender mailSender = createMailSender(tenantId);
            if (mailSender == null) {
                log.warn("SMTP not configured, skipping email send");
                return;
            }
            final Tenant tenant = tenantService.findByTenantId(tenantId).orElseThrow(()
                    -> new InvalidParameterException(IdErrorCode.TENANT_NOT_FOUND));


            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");

            // Get SMTP settings for sender email
            SmtpSettingsDto smtpSettings = getSmtpSettings(tenantId);

            assert smtpSettings != null;
            helper.setFrom(smtpSettings.getSmtpEmail(), tenant.getDomain());
            helper.setTo(recipientEmail);
            helper.setSubject(subject);
            helper.setText(htmlContent, true); // true indicates HTML content

            mailSender.send(message);
            log.info("Email sent successfully to: {}", recipientEmail);

        } catch (MessagingException e) {
            log.error("Failed to send email to: {}, error: {}", recipientEmail, e.getMessage());
        } catch (Exception e) {
            log.error("Unexpected error sending email to: {}, error: {}", recipientEmail, e.getMessage());
        }
    }

    private JavaMailSender createMailSender(String tenantId) {
        try {
            SmtpSettingsDto smtpSettings = getSmtpSettings(tenantId);
            if (smtpSettings == null || !smtpSettings.getStatus()) {
                return null;
            }

            JavaMailSenderImpl mailSender = new JavaMailSenderImpl();
            mailSender.setHost(smtpSettings.getSmtpHost());
            mailSender.setPort(smtpSettings.getSmtpPort());
            mailSender.setUsername(smtpSettings.getSmtpEmail());
            mailSender.setPassword(smtpSettings.getSmtpPassword());

            // Configure properties based on encryption type
            Properties props = mailSender.getJavaMailProperties();
            props.put("mail.transport.protocol", "smtp");
            props.put("mail.smtp.auth", "true");
            props.put("mail.smtp.connectiontimeout", "10000");
            props.put("mail.smtp.timeout", "10000");
            props.put("mail.smtp.writetimeout", "10000");

            // Configure encryption
            String encryption = smtpSettings.getEncryption();
            if ("tls".equalsIgnoreCase(encryption)) {
                props.put("mail.smtp.starttls.enable", "true");
                props.put("mail.smtp.starttls.required", "true");
            } else if ("ssl".equalsIgnoreCase(encryption)) {
                props.put("mail.smtp.ssl.enable", "true");
                props.put("mail.smtp.socketFactory.port", smtpSettings.getSmtpPort().toString());
                props.put("mail.smtp.socketFactory.class", "javax.net.ssl.SSLSocketFactory");
                props.put("mail.smtp.socketFactory.fallback", "false");
            }

            return mailSender;
        } catch (Exception e) {
            log.error("Failed to create mail sender: {}", e.getMessage());
            return null;
        }
    }

    private SmtpSettingsDto getSmtpSettings(String tenantId) {
        try {

            ConnectionSettingsDto connectionSettings = connectionSettingsService.getSettingsById(tenantId);
            return connectionSettings.getSmtp();
        } catch (Exception e) {
            log.error("Failed to get SMTP settings: {}", e.getMessage());
            return null;
        }
    }


}