package tndung.vnfb.smm.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tndung.vnfb.smm.config.AuditContextHolder;
import tndung.vnfb.smm.constant.TemplateTypes;
import tndung.vnfb.smm.constant.enums.CommonStatus;
import tndung.vnfb.smm.dto.connections.ConnectionSettingsDto;
import tndung.vnfb.smm.dto.connections.TelegramBotSettingsDto;
import tndung.vnfb.smm.dto.request.ApiProviderEditReq;
import tndung.vnfb.smm.dto.request.ApiProviderReq;
import tndung.vnfb.smm.dto.response.ApiProviderRes;
import tndung.vnfb.smm.dto.response.smm.SMMBalanceRes;
import tndung.vnfb.smm.entity.ApiProvider;
import tndung.vnfb.smm.entity.TelegramTemplate;
import tndung.vnfb.smm.exception.IdErrorCode;
import tndung.vnfb.smm.exception.InvalidParameterException;
import tndung.vnfb.smm.helper.CommonHelper;
import tndung.vnfb.smm.mapper.ApiProviderMapper;
import tndung.vnfb.smm.repository.tenant.ApiProviderRepository;
import tndung.vnfb.smm.repository.tenant.TelegramTemplateRepository;
import tndung.vnfb.smm.rest.SMMConsumer;
import tndung.vnfb.smm.service.*;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
public class ApiProviderServiceImpl implements ApiProviderService {

    private final ApiProviderMapper mapper;
    private final SMMConsumer smmConsumer;
    private final ApiProviderRepository repository;
    private final CurrencyService currencyService;
    private final TelegramService telegramService;
    private final ConnectionSettingsService connectionSettingsService;
    private final TelegramTemplateRepository telegramTemplateRepository;
    private final AuthenticationFacade authenticationFacade;

    @Value("${currency.api.base-currency}")
    private String baseCurrencyCode;

    @Override
    @Transactional
    public ApiProviderRes checkBalance(Long id) {
        final ApiProvider provider = findByIsDeletedFalse(id);
        try {
            setBalance(provider);
        } catch (Exception ex) {
            provider.setStatus(CommonStatus.DEACTIVATED);
        }
        provider.setStatus(CommonStatus.ACTIVATED);
        return mapper.toRes(repository.save(provider));
    }

    @Override
    @Transactional
    public void checkBalance(ApiProvider apiProvider) {
        setBalance(apiProvider);
        repository.save(apiProvider);
    }

    private void setBalance(ApiProvider provider) {
        final SMMBalanceRes rs = smmConsumer.getBalance(provider);
        if (rs.getBalance() == null) provider.setStatus(CommonStatus.DEACTIVATED);

        // Store original balance before conversion
        provider.setPreviewBalance(rs.getBalance());

        BigDecimal amount = currencyService.convert(rs.getBalance(), rs.getCurrency(), baseCurrencyCode);

        provider.setBalance(amount);
        provider.setCurrency(rs.getCurrency());

        if (provider.getBalanceAlert() != null && provider.getBalanceAlert().compareTo(amount) >= 0
        && provider.wasSentInLast24Hours()
        ) {
            sendBalanceWarningNotification(provider.getTenantId(), provider);
        }
    }

    private void sendBalanceWarningNotification(String tenantId, ApiProvider apiProvider) {
        try {
            log.debug("Checking Balance Warning notification for provider: {}", apiProvider.getName());

            // Check if notification was sent in last 24 hours for this provider
            Optional<ApiProvider> providerOpt = repository
                    .findByIdAndNotSentInLast24Hours(
                            apiProvider.getId(),
                            OffsetDateTime.now().minusHours(24)
                    );

            if (providerOpt.isEmpty()) {
                log.debug("Balance Warning notification was already sent in last 24 hours for provider: {}",
                        apiProvider.getName());
                return;
            }

            // Get the template for sending notification
            Optional<TelegramTemplate> templateOpt = telegramTemplateRepository
                    .findByTenantIdAndType(tenantId, TemplateTypes.Telegram.PROVIDER_BALANCE_WARING);

            if (templateOpt.isEmpty()) {
                log.debug("No telegram template found for type: {}", TemplateTypes.Telegram.PROVIDER_BALANCE_WARING);
                return;
            }

            TelegramTemplate template = templateOpt.get();

            log.debug("Sending Balance Warning notification for provider: {}", apiProvider.getName());

            // Get connection settings
            ConnectionSettingsDto connectionSettings = connectionSettingsService.getSettingsById(tenantId);
            TelegramBotSettingsDto telegramSettings = connectionSettings.getTelegramBot();

            // Send notification with provider data
            telegramService.sendBalanceWarningNotificationAdmin(
                    telegramSettings,
                    tenantId,
                    apiProvider.getName(),
                    apiProvider.getBalance() + "$",
                    CommonHelper.formatTimeToNotification(OffsetDateTime.now(), AuditContextHolder.getUserZone())
            );

            // Update last notification timestamp for this provider
            apiProvider.updateLastNotiAt();
            repository.save(apiProvider);

            log.debug("Successfully sent Balance Warning notification for provider {}", apiProvider.getName());

        } catch (Exception e) {
            log.error("Failed to send Balance Warning notification for provider: {}, error: {}",
                    apiProvider.getName(), e.getMessage());
            // Don't rethrow - we don't want to break the entire notification process
        }
    }

    @Override
    public ApiProviderRes deactivate(Long id) {
        final ApiProvider provider = findByIsDeletedFalse(id);
        provider.setStatus(CommonStatus.DEACTIVATED);
        return mapper.toRes(repository.save(provider));
    }

    @Override
    public ApiProviderRes active(Long id) {
        final ApiProvider provider = findByIsDeletedFalse(id);
        provider.setStatus(CommonStatus.ACTIVATED);
        return mapper.toRes(repository.save(provider));
    }

    @Override
    public ApiProviderRes add(ApiProviderReq req) {
        final List<ApiProvider> providers = repository.findByUrl(req.getUrl());

        final String name = req.getUrl().replaceAll("http(s)?://|www\\.|/.*", "");
        if (providers != null && !providers.isEmpty()) {
            if (providers.stream().anyMatch(i -> Boolean.FALSE.equals(i.getIsDeleted())))
                throw new InvalidParameterException(IdErrorCode.PROVIDER_URL_EXISTS);
        }

        final ApiProvider apiProvider = mapper.toEntity(req);

        // get balance
        setBalance(apiProvider);
        apiProvider.setName(name);
        apiProvider.setEnableRate(req.getEnableRate());
        final ApiProvider apiProviderSaved = repository.save(apiProvider);

        return mapper.toRes(apiProviderSaved);
    }

    @Override
    public ApiProvider findById(Long id) {
        return repository.findById(id).orElseThrow(() -> new InvalidParameterException(IdErrorCode.PROVIDER_NOT_EXISTS));
    }

    @Override
    public ApiProvider findByIsDeletedFalse(Long id) {
        return repository.findByIsDeletedFalse(id).orElseThrow(() -> new InvalidParameterException(IdErrorCode.PROVIDER_NOT_EXISTS));
    }

    @Override
    public ApiProviderRes getById(Long id) {
        final ApiProvider apiProvider = findByIsDeletedFalse(id);
        return mapper.toRes(apiProvider);
    }

    @Override
    public ApiProviderRes edit(Long id, ApiProviderEditReq req) {
        final Optional<ApiProvider> providerOpt = repository.findByUrlWithDeletedFalse(req.getUrl());
        if (providerOpt.isPresent() && !providerOpt.get().getUrl()
                .equals(req.getUrl())) throw new InvalidParameterException(IdErrorCode.PROVIDER_URL_EXISTS);

        final ApiProvider provider = findByIsDeletedFalse(id);

        provider.setUrl(req.getUrl());
        if (!Strings.isBlank(req.getSecretKey())) {
            provider.setSecretKey(req.getSecretKey());
        }
        if (req.getCurrencyRate() != null) {
            provider.setCurrencyRate(req.getCurrencyRate());
        }

        if (req.getEnableRate() != null) {
            provider.setEnableRate(req.getEnableRate());
        }

        setBalance(provider);

        provider.setName(req.getName());
        provider.setBalanceAlert(req.getBalanceAlert());

        final ApiProvider apiProviderSaved = repository.save(provider);

        return mapper.toRes(apiProviderSaved);
    }

    @Override
    @Transactional
    public void changeApiKey(Long id, String apiKey) {
        final ApiProvider apiProvider = findById(id);
        apiProvider.setSecretKey(apiKey);
        setBalance(apiProvider);
        repository.save(apiProvider);
    }

    @Override
    @Transactional
    public void changeBalanceAlert(Long id, BigDecimal balanceAlert) {
        final ApiProvider apiProvider = findByIsDeletedFalse(id);
        apiProvider.setBalanceAlert(balanceAlert);
        repository.save(apiProvider);
    }

    @Override
    public void delete(Long id) {
        final ApiProvider apiProvider = findByIsDeletedFalse(id);
        apiProvider.setIsDeleted(true);
        repository.save(apiProvider);
    }

    @Override
    public List<ApiProviderRes> getAllByTenant() {
        return mapper.toRes(repository.getAllByTenant());
    }


    @Override
    public List<ApiProvider> getAll() {
        return repository.getAll();
    }

    @Override
    @Transactional
    public void checkAllBalance() {
        final List<ApiProvider> apiProviders = repository.findAll();
        for (ApiProvider provider : apiProviders) {
            try {
                setBalance(provider);
                repository.save(provider);
            } catch (Exception ignored) {
            }
        }
    }

    @Override
    public ApiProvider save(ApiProvider apiProvider) {
        return repository.save(apiProvider);
    }

    @Override
    public ApiProvider findByTenantId(String tenantId) {
        return repository.findFirstByTenantId(tenantId).orElse(null);
    }
}
