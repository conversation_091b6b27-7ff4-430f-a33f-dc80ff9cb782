package tndung.vnfb.smm.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tndung.vnfb.smm.config.SubscriptionProperties;
import tndung.vnfb.smm.config.TenantContext;
import tndung.vnfb.smm.constant.Common;
import tndung.vnfb.smm.constant.enums.Currency;
import tndung.vnfb.smm.constant.enums.Role;
import tndung.vnfb.smm.constant.enums.TenantStatus;
import tndung.vnfb.smm.constant.enums.TransactionType;
import tndung.vnfb.smm.dto.GeneralSettingsDto;
import tndung.vnfb.smm.dto.request.*;
import tndung.vnfb.smm.dto.response.GUserRes;
import tndung.vnfb.smm.dto.response.TenantHierarchyResponse;
import tndung.vnfb.smm.entity.ApiProvider;
import tndung.vnfb.smm.entity.GUser;
import tndung.vnfb.smm.entity.Tenant;
import tndung.vnfb.smm.exception.DynamicErrorCode;
import tndung.vnfb.smm.exception.IdErrorCode;
import tndung.vnfb.smm.exception.InvalidParameterException;
import tndung.vnfb.smm.repository.nontenant.TenantRepository;
import tndung.vnfb.smm.service.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static tndung.vnfb.smm.constant.Common.MAIN_TENANT;

@Service
@RequiredArgsConstructor
@Slf4j
public class TenantServiceImpl implements TenantService {

    private final TenantRepository tenantRepository;
    private final SubscriptionProperties subscriptionProperties;
    private final BalanceService balanceService;
    private final AuthenticationFacade authenticationFacade;
    private final RedisTemplate<String, Object> redisTemplate;
    private final ObjectMapper objectMapper;
    private final UserTenantAccessService userTenantAccessService;

    @Autowired
    @Lazy
    private TenantService self;
//    @Autowired
//    private ApiProviderService apiProviderService;



    private final ApplicationContext applicationContext;

    // Lazy getter for TenantService to break circular dependency
    private ApiProviderService getApiProviderService() {
        return applicationContext.getBean(ApiProviderService.class);
    }

    private GUserService getGUserService() {
        return applicationContext.getBean(GUserService.class);
    }

    @Cacheable(value = "tenantsByDomain", key = "#domain")
    @Override
    public Optional<Tenant> findByDomain(String domain) {
        return tenantRepository.findByDomain(domain);
    }

    @Override
    @Cacheable(value = "tenantsById", key = "#id", condition = "#id != null")
    public Optional<Tenant> findByTenantId(String id) {
        if (id == null || id.trim().isEmpty()) {
            return Optional.empty();
        }
        return tenantRepository.findById(id);
    }

    @Override
    @Cacheable(value = "mainTenant")
    public Optional<Tenant> findMainTenant() {
        return tenantRepository.findByMainIsTrue();
    }

    @Override
    public List<Tenant> findAll() {
        return tenantRepository.findAll();
    }

    @Override
    public boolean isMainTenantSite() {
        return MAIN_TENANT.equals(TenantContext.getSiteTenant());
    }

    @Override
    public String getTenantId() {
        if (isMainTenantSite()) {
            return TenantContext.getCurrentTenant();
        }
        return TenantContext.getSiteTenant();
    }

    @Override
    public List<Tenant> findByStatus(TenantStatus status) {
        return tenantRepository.findByStatus(status);
    }

    @Override
    @CacheEvict(value = {"tenantsByDomain", "tenantsById", "tenantInfo"}, allEntries = true)
    public Tenant save(Tenant tenant) {
        return tenantRepository.save(tenant);
    }

    @Override
    public Tenant save(DomainReq req) {
        Tenant tenant = new Tenant();
        tenant.setDomain(req.getDomain());
        tenant.setStatus(req.getStatus() != null ? req.getStatus() : TenantStatus.New);
        tenant.setId(UUID.randomUUID().toString());
        tenant.setApiUrl(req.getApiUrl());
        tenant.setSiteUrl(req.getSiteUrl());
        tenant.setContactEmail(req.getContactEmail());
        tenant.setIsDeleted(false);
        return save(tenant);
    }

    @Override
    @Transactional
    @CacheEvict(value = {"tenantsByDomain", "tenantsById"}, allEntries = true)
    public void delete(String domain) {
        tenantRepository.deleteByDomain(domain);
    }

    @Override
    @Transactional
    @CacheEvict(value = {"tenantsByDomain", "tenantsById", "tenantInfo"}, allEntries = true)
    public void updateDiscountSystemEnabled(boolean enabled) {
        String tenantId = TenantContext.getCurrentTenant();
        Tenant tenant = tenantRepository.findById(tenantId)
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.TENANT_NOT_FOUND));

        tenant.setEnableDiscountSystem(enabled);
        tenantRepository.save(tenant);
    }

    @Override
    public List<Tenant> findSubTenants(String parentTenantId) {
        return tenantRepository.findByParentTenantIdAndIsDeletedFalseOrderByCreatedAt(parentTenantId);
    }

    @Override
    public List<Tenant> findAllParentTenants() {
        return tenantRepository.findAllParentTenants();
    }

    @Override
    public List<Tenant> findAllSubTenants() {
        return tenantRepository.findAllSubTenants();
    }

    @Override
    public Optional<Tenant> findParentTenant(String subTenantId) {
        return tenantRepository.findParentTenant(subTenantId);
    }

    @Override
    public TenantHierarchyResponse getParentTenantHierarchy(String subTenantId) {
        Optional<Tenant> parentTenant = findParentTenant(subTenantId);
        if (parentTenant.isPresent()) {
            return convertToHierarchyResponse(parentTenant.get());
        }
        throw new InvalidParameterException(IdErrorCode.TENANT_NOT_FOUND);
    }


    @Override
    public boolean canCreateSubTenant(String tenantId) {
        Boolean canCreate = tenantRepository.canCreateSubTenant(tenantId);
        return canCreate != null && canCreate;
    }


    @Override
    public boolean isSubTenant(String tenantId) {
        Boolean result = tenantRepository.isSubTenant(tenantId);
        return result != null && result;
    }

    @Override
    @Transactional
    public TenantHierarchyResponse requestChildTenant(ChildTenantReq req) {
        // Get current user first - fail fast if not authenticated
        final GUser currentUser = authenticationFacade.getCurrentUser()
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.USER_NOT_FOUND));

        // Handle existing tenant logic
        handleExistingTenant(req.getDomain());

        Tenant currentTenant = self.findByTenantId(TenantContext.getCurrentTenant())
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.TENANT_NOT_FOUND));

        // Calculate and process payment
        processSubscriptionPayment(currentTenant, currentUser);

        // Create and save new tenant
        Tenant newTenant = createNewTenant(req, currentUser);

        // Cache request information
        cacheRequestInfo(newTenant.getId(), req, currentUser);

        return convertToHierarchyResponse(tenantRepository.save(newTenant));
    }

    private Tenant handleExistingTenant(String domain) {
        Optional<Tenant> existingTenant = tenantRepository.findByDomain(domain);

        if (existingTenant.isPresent()) {
            Tenant tenant = existingTenant.get();

            // Only allow if tenant was previously rejected
            if (tenant.getStatus() != TenantStatus.Reject) {
                throw new InvalidParameterException(IdErrorCode.DOMAIN_ALREADY_EXISTS);
            }

            // Clean up rejected tenant
            tenantRepository.deleteByDomain(domain);
            return tenant;
        }

        // Return a new tenant object if none exists
        return new Tenant();
    }

    private void processSubscriptionPayment(Tenant tenant, GUser currentUser) {
        BigDecimal baseCost = calculatePanelPrice(tenant);

        String note = String.format("Subscription fee for %d days (rate: %s/%d days)",
                30, baseCost, 30);

        balanceService.deductBalance(
                currentUser,
                baseCost,
                TransactionType.Spent,
                "SUBSCRIPTION",
                note
        );
    }

    private Tenant createNewTenant(ChildTenantReq req, GUser currentUser) {
        Tenant newTenant = new Tenant();
        newTenant.setId(UUID.randomUUID().toString());
        newTenant.setDomain(req.getDomain());
        newTenant.setApiUrl(); // Consider setting a proper API URL here
        newTenant.setParentTenantId(TenantContext.getSiteTenant());
        newTenant.setStatus(TenantStatus.Waiting);
        newTenant.setIsDeleted(false);
        newTenant.setMain(false);
        newTenant.setSiteUrl("https://" + req.getDomain());
        newTenant.setOwnerId(currentUser.getId());

        return newTenant;
    }

    private void cacheRequestInfo(String tenantId, ChildTenantReq req, GUser currentUser) {
        // Update request with current user info
        req.setEmail(currentUser.getEmail());
        req.setPhone(currentUser.getPhone());

        // Cache the request information
        String cacheKey = Common.Redis.DOMAIN_REQUEST_INFO + tenantId;
        redisTemplate.opsForValue().set(cacheKey, req);
    }

    @Override
    public TenantHierarchyResponse approveSubTenant(String tenantId) {
        final Optional<Tenant> existsTenant = self.findByTenantId(tenantId);
        if (existsTenant.isEmpty()) {
            throw new InvalidParameterException(IdErrorCode.TENANT_NOT_FOUND);
        }

        final Tenant parrentTenant = self.findByTenantId(existsTenant.get().getParentTenantId())
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.TENANT_NOT_FOUND));

        final Tenant subTenant = existsTenant.get();



        self.deductUserBalance(subscriptionProperties.getDefaultRenewalDays(), subTenant);


        if (subTenant.getStatus() != TenantStatus.Waiting) {
            throw new InvalidParameterException(IdErrorCode.TENANT_NOT_FOUND);
        }
        subTenant.setStatus(TenantStatus.New);
        // create admin user:
        ChildTenantReq childTenantReq = (ChildTenantReq) redisTemplate.opsForValue()
                .get(Common.Redis.DOMAIN_REQUEST_INFO + tenantId);
        if (childTenantReq == null) {
            throw new InvalidParameterException(IdErrorCode.TENANT_NOT_FOUND);
        }

        GUserRes userRes = getGUserService().create(UserReq.builder().userName(childTenantReq.getAdminUserName())
                .password(childTenantReq.getAdminPassword())
                .email(childTenantReq.getEmail())
                .phone(childTenantReq.getPhone())
                .build(), List.of(Role.ADMIN_PANEL, Role.USER));

        GUser owner = getGUserService().findById(subTenant.getOwnerId());

        // create api provider
        ApiProvider apiProvider = new ApiProvider();
        apiProvider.setTenantId(subTenant.getId());
        apiProvider.setName(parrentTenant.getDomain());
        apiProvider.setUrl(String.format("https://%s/api/v2", parrentTenant.getDomain()));
        apiProvider.setSecretKey(owner.getSecretKey());
        apiProvider.setBalance(owner.getBalance());
        apiProvider.setPreviewBalance(owner.getBalance());
        apiProvider.setCurrency(Currency.USD.name());
        getApiProviderService().save(apiProvider);


        userTenantAccessService.grantTenantAccess(userRes.getId(), tenantId);

        redisTemplate.delete(Common.Redis.DOMAIN_REQUEST_INFO + tenantId);
        return convertToHierarchyResponse(tenantRepository.save(subTenant));

    }


    @Override
    public TenantHierarchyResponse rejectSubTenant(String tenantId) {
        Optional<Tenant> existsTenant = tenantRepository.findById(tenantId);
        if (existsTenant.isEmpty()) {
            throw new InvalidParameterException(IdErrorCode.TENANT_NOT_FOUND);
        }


        Tenant tenant = existsTenant.get();
        if (tenant.getStatus() != TenantStatus.Waiting) {
            throw new InvalidParameterException(IdErrorCode.TENANT_NOT_FOUND);
        }
        tenant.setStatus(TenantStatus.Active);
        return convertToHierarchyResponse(tenantRepository.save(tenant));
    }

    @Override
    @Transactional
    public void suspendChildTenants(String tenantId) {
        List<Tenant> childTenants = findSubTenants(TenantContext.getCurrentTenant());
        Optional<Tenant> tenantOpt = childTenants.stream()
                .filter(t -> t.getId().equals(tenantId)).findFirst();
        if (tenantOpt.isEmpty()) {
            throw new InvalidParameterException(IdErrorCode.TENANT_NOT_FOUND);
        }
        final Tenant tenant = tenantOpt.get();
        tenant.setStatus(TenantStatus.Suspended);
        tenantRepository.save(tenant);
    }

    @Override
    public void activateChildTenants(String tenantId) {
        List<Tenant> childTenants = findSubTenants(TenantContext.getCurrentTenant());
        Optional<Tenant> tenantOpt = childTenants.stream()
                .filter(t -> t.getId().equals(tenantId)).findFirst();
        if (tenantOpt.isEmpty()) {
            throw new InvalidParameterException(IdErrorCode.TENANT_NOT_FOUND);
        }
        final Tenant tenant = tenantOpt.get();
        tenant.setStatus(TenantStatus.Active);
        tenantRepository.save(tenant);
    }

    @Override
    public TenantHierarchyResponse extendChildSubscription(String tenantId, ExtendTenantSubscriptionReq request) {
        List<Tenant> childTenants = findSubTenants(TenantContext.getCurrentTenant());
        Optional<Tenant> tenantOpt = childTenants.stream()
                .filter(t -> t.getId().equals(tenantId)).findFirst();


        if (tenantOpt.isEmpty()) {
            throw new InvalidParameterException(IdErrorCode.TENANT_NOT_FOUND);
        }
        Tenant tenant = tenantOpt.get();

        self.deductUserBalance(request.getExtensionDays(), tenant);

        ZonedDateTime currentEndDate = tenant.getSubscriptionEndDate();
        ZonedDateTime newEndDate;

        if (currentEndDate == null || currentEndDate.isBefore(ZonedDateTime.now())) {
            newEndDate = ZonedDateTime.now().plusDays(request.getExtensionDays());
        } else {
            newEndDate = currentEndDate.plusDays(request.getExtensionDays());
        }

        tenant.setSubscriptionEndDate(newEndDate);

        if (tenant.getStatus() == TenantStatus.Expired || tenant.getStatus() == TenantStatus.Suspended) {
            tenant.setStatus(TenantStatus.Active);
        }

        Tenant savedTenant = tenantRepository.save(tenant);
        return convertToHierarchyResponse(savedTenant);
    }

    public TenantHierarchyResponse convertToHierarchyResponse(Tenant tenant) {
        return TenantHierarchyResponse.builder()
                .id(tenant.getId())
                .domain(tenant.getDomain())
                .status(tenant.getStatus())
                .apiUrl(tenant.getApiUrl())
                .siteUrl(tenant.getSiteUrl())
                .contactEmail(tenant.getContactEmail())
                .main(tenant.getMain())
                .createdAt(tenant.getCreatedAt())
                .parentTenantId(tenant.getParentTenantId())
                .daysUntilExpiration(tenant.getDaysUntilExpiration())
                .lastRenewalDate(tenant.getLastRenewalDate())
                .subscriptionEndDate(tenant.getSubscriptionEndDate())
                .subscriptionStartDate(tenant.getSubscriptionStartDate())
                .build();
    }

    public Tenant convertToEntity(SubTenantCreateRequest request) {
        Tenant tenant = new Tenant();
        tenant.setDomain(request.getDomain());
        tenant.setApiUrl(request.getApiUrl());
        tenant.setSiteUrl(request.getSiteUrl());
        tenant.setContactEmail(request.getContactEmail());
        tenant.setStatus(request.getStatus());
        tenant.setMain(false);

        if (request.getDefaultLanguage() != null) {
            tenant.setDefaultLanguage(request.getDefaultLanguage());
        }
        if (request.getAvailableLanguages() != null) {
            tenant.setAvailableLanguages(request.getAvailableLanguages());
        }
        if (request.getAvailableCurrencies() != null) {
            tenant.setAvailableCurrencies(request.getAvailableCurrencies());
        }
        if (request.getEnableAffiliate() != null) {
            tenant.setEnableAffiliate(request.getEnableAffiliate());
        }
        if (request.getEnableDiscountSystem() != null) {
            tenant.setEnableDiscountSystem(request.getEnableDiscountSystem());
        }
        if (request.getDesignSettings() != null) {
            tenant.setDesignSettings(request.getDesignSettings());
        }
        if (request.getDecimalPlaces() != null) {
            tenant.setDecimalPlaces(request.getDecimalPlaces());
        }
        if (request.getConnectionSettings() != null) {
            tenant.setConnectionSettings(request.getConnectionSettings());
        }
        if (request.getAverageTimeSettings() != null) {
            tenant.setAverageTimeSettings(request.getAverageTimeSettings());
        }

        return tenant;
    }

    /**
     * Trừ tiền từ user hiện tại theo cài đặt giá tiền cho số ngày được chỉ định
     * Tính lại giá theo tỷ lệ với 30 ngày mặc định
     *
     * @param days số ngày cần tính phí
     * @return balance mới sau khi trừ tiền
     * @throws InvalidParameterException nếu user không đủ tiền
     */
    @Transactional
    public void deductUserBalance(int days, Tenant subTenant) {
        if (days <= 0) {
            throw new InvalidParameterException(new DynamicErrorCode(4300, "Number of days must be greater than 0", 400));
        }

        // Lấy user hiện tại
        var currentUser = authenticationFacade.getCurrentUser();

        if (currentUser.isEmpty()) {
            throw new InvalidParameterException(IdErrorCode.USER_NOT_FOUND);
        }

        // Tính giá theo tỷ lệ với 30 ngày mặc định
        BigDecimal baseCost = calculatePanelPrice(subTenant);
        int defaultDays = subscriptionProperties.getDefaultRenewalDays();

        BigDecimal calculatedCost = baseCost
                .multiply(BigDecimal.valueOf(days))
                .divide(BigDecimal.valueOf(defaultDays), 2, RoundingMode.HALF_UP);

        // Tạo note cho transaction
        String note = String.format("Subscription fee for %d days (rate: %s/%d days)",
                days, baseCost, defaultDays);

        // Sử dụng BalanceService để trừ tiền
        balanceService.deductBalance(
                currentUser.get(),
                calculatedCost,
                TransactionType.Spent,
                "SUBSCRIPTION",
                note
        );

    }

    @Override
    public List<Tenant> findTenantsByOwnerId(Long ownerId) {
        return tenantRepository.findByOwnerIdAndIsDeletedFalse(ownerId);
    }







    /**
     * Parse general settings from JSON string
     */
    public GeneralSettingsDto parseGeneralSettings(String generalSettingsJson) {
        if (generalSettingsJson == null || generalSettingsJson.trim().isEmpty()) {
            return GeneralSettingsDto.builder()
                    .panelPricePercentage(new BigDecimal("7.7"))
                    .build();
        }

        try {
            return objectMapper.readValue(generalSettingsJson, GeneralSettingsDto.class);
        } catch (Exception e) {
            log.warn("Failed to parse general settings JSON: {}", e.getMessage());
            return GeneralSettingsDto.builder()
                    .panelPricePercentage(new BigDecimal("7.7"))
                    .build();
        }
    }

    /**
     * Calculate panel price based on tenant hierarchy and percentage
     */
    public BigDecimal calculatePanelPrice(Tenant tenant) {
        GeneralSettingsDto generalSettings = parseGeneralSettings(tenant.getGeneralSettings());
        BigDecimal percentage = generalSettings.getPanelPricePercentage();

        // Get base price from main tenant or parent tenant
        BigDecimal basePrice = getBasePriceForTenant(tenant);

        // Calculate final price: basePrice + (basePrice * percentage / 100)
        // Example: $7.7 + 10% = $7.7 + ($7.7 * 10 / 100) = $7.7 + $0.77 = $8.47
        BigDecimal additionalAmount = basePrice.multiply(percentage).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP);
        return basePrice.add(additionalAmount);
    }

    @Override
    public BigDecimal getCurrentBasePriceForCurrentTenant() {
        final Tenant currentTenant = self.findByTenantId(TenantContext.getCurrentTenant())
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.TENANT_NOT_FOUND));
        return getBasePriceForTenant(currentTenant);
    }

    /**
     * Get base price for tenant based on hierarchy
     */
    private BigDecimal getBasePriceForTenant(Tenant tenant) {
        if (tenant.getParentTenantId() == null) {
            // Current tenant has no parent, get price from main tenant
            Tenant mainTenant = tenantRepository.findByMainIsTrue()
                    .orElseThrow(() -> new RuntimeException("Main tenant not found"));

            GeneralSettingsDto mainSettings = parseGeneralSettings(mainTenant.getGeneralSettings());
            return mainSettings.getBasePrice();
        } else {
            // Current tenant has parent, get price from parent tenant
            Tenant parentTenant = tenantRepository.findById(tenant.getParentTenantId())
                    .orElseThrow(() -> new RuntimeException("Parent tenant not found"));

            // Calculate parent's price first
            return calculatePanelPrice(parentTenant);
        }
    }
}