-- =====================================================
-- TENANT DEFAULT DATA INITIALIZATION SCRIPT
-- File path: src/main/resources/sql/tenant-default-data.sql
--
-- Description: This script initializes default data for new tenants
-- Placeholder {tenantId} will be replaced with actual tenant ID
-- =====================================================

-- 1. INSERT DEFAULT EMAIL TEMPLATES
-- =====================================================

-- Password Reset Email Template
INSERT INTO public.email_templates (tenant_id, type, name, icon, subject, content, enabled, created_at, updated_at, variables)
VALUES (
           '{tenantId}',
           'password_reset',
           'Đặt lại mật khẩu (User)',
           'fa-key',
           'Đặt lại mật khẩu - {domain}',
           E'<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
<div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
<div style="text-align: center; margin-bottom: 30px;">
<h1 style="color: #ffc107; margin: 0; font-size: 28px;">🔑 Đặt lại mật khẩu</h1>
</div>
<p style="font-size: 16px; color: #333; margin-bottom: 20px;">Xin chào <strong>{username}</strong>,</p>
<p style="font-size: 16px; color: #333; margin-bottom: 25px;">Bạn đã yêu cầu đặt lại mật khẩu cho tài khoản của mình.</p>
<div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 25px;">
<h3 style="color: #495057; margin-top: 0; margin-bottom: 15px;">🔗 Liên kết đặt lại mật khẩu:</h3>
<div style="text-align: center; margin: 20px 0;"><a style="display: inline-block; background-color: #007bff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold;" href="{reset_link}">Đặt lại mật khẩu</a></div>
<p style="margin: 10px 0 0 0; color: #666; font-size: 14px; word-break: break-all;">Hoặc copy link này: {reset_link}</p>
</div>
<div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 25px;">
<h3 style="color: #495057; margin-top: 0; margin-bottom: 15px;">⏰ Thông tin hết hạn:</h3>
<table style="width: 100%; border-collapse: collapse;">
<tbody>
<tr>
<td style="padding: 8px 0; color: #666;"><strong>Liên kết sẽ hết hạn vào:</strong></td>
<td style="padding: 8px 0; color: #dc3545; font-weight: bold;">{expiry_time}</td>
</tr>
</tbody>
</table>
</div>
<div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 8px; margin-bottom: 25px;">
<p style="margin: 0; color: #856404; font-size: 14px;">⚠️ Nếu bạn không yêu cầu đặt lại mật khẩu, vui lòng bỏ qua email này.</p>
</div>
<div style="text-align: center; border-top: 1px solid #eee; padding-top: 20px;">
<p style="margin: 0; color: #666; font-size: 14px;">Trân trọng,</p>
<p style="margin: 5px 0 0 0; color: #333; font-weight: bold;">Đội ngũ hỗ trợ {domain}</p>
<p style="margin: 5px 0 0 0; color: #666; font-size: 14px;">Email: {support_email}</p>
</div>
</div>
</div>',
           true,
           CURRENT_TIMESTAMP,
           CURRENT_TIMESTAMP,
           '{username},{email},{reset_link},{expiry_time},{domain},{support_email}'
       );

-- Email Verification Template
INSERT INTO public.email_templates (tenant_id, type, name, icon, subject, content, enabled, created_at, updated_at, variables)
VALUES (
           '{tenantId}',
           'email_verification',
           'Xác thực địa chỉ email (User)',
           'fa-envelope',
           'Xác thực địa chỉ email - {domain}',
           E'<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Xác thực email</title>
</head>
<body>
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
        <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <div style="text-align: center; margin-bottom: 30px;">
                <h1 style="color: #17a2b8; margin: 0; font-size: 28px;">📧 Xác thực email!</h1>
            </div>
            <p style="font-size: 16px; color: #333; margin-bottom: 20px;">Xin chào <strong>{username}</strong>,</p>
            <p style="font-size: 16px; color: #333; margin-bottom: 25px;">Cảm ơn bạn đã đăng ký tài khoản tại {domain}. Để hoàn tất quá trình đăng ký, vui lòng xác thực địa chỉ email của bạn.</p>
            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 25px;">
                <h3 style="color: #495057; margin-top: 0; margin-bottom: 15px;">👤 Thông tin tài khoản:</h3>
                <table style="width: 100%; border-collapse: collapse;">
                    <tr><td style="padding: 8px 0; color: #666;"><strong>Tên đăng nhập:</strong></td><td style="padding: 8px 0; color: #333;">{username}</td></tr>
                    <tr><td style="padding: 8px 0; color: #666;"><strong>Địa chỉ email:</strong></td><td style="padding: 8px 0; color: #333;">{email}</td></tr>
                </table>
            </div>
            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 25px;">
                <h3 style="color: #495057; margin-top: 0; margin-bottom: 15px;">🔗 Xác thực email:</h3>
                <div style="text-align: center; margin: 20px 0;">
                    <a href="{verification_link}" style="display: inline-block; background-color: #17a2b8; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold;">Xác thực email</a>
                </div>
                <p style="margin: 10px 0 0 0; color: #666; font-size: 14px;">Nếu bạn gặp khó khăn trong việc nhấp vào liên kết, hãy sao chép và dán URL này vào trình duyệt:</p>
                <p style="margin: 10px 0 0 0; color: #666; font-size: 14px; word-break: break-all; background-color: #e9ecef; padding: 10px; border-radius: 4px;">{verification_link}</p>
            </div>
            <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 8px; margin-bottom: 25px;">
                <p style="margin: 0; color: #856404; font-size: 14px;">⏰ Liên kết này sẽ hết hạn sau 24 giờ. Nếu bạn không thực hiện yêu cầu này, vui lòng bỏ qua email này.</p>
            </div>
            <div style="text-align: center; border-top: 1px solid #eee; padding-top: 20px;">
                <p style="margin: 0; color: #666; font-size: 14px;">Trân trọng,</p>
                <p style="margin: 5px 0 0 0; color: #333; font-weight: bold;">Đội ngũ hỗ trợ {domain}</p>
                <p style="margin: 5px 0 0 0; color: #666; font-size: 14px;">Email: {support_email}</p>
            </div>
        </div>
    </div>
</body>
</html>',
           true,
           CURRENT_TIMESTAMP,
           CURRENT_TIMESTAMP,
           '{username},{email},{verification_link},{domain},{support_email}'
       );

-- Deposit Notification Template
INSERT INTO public.email_templates (tenant_id, type, name, icon, subject, content, enabled, created_at, updated_at, variables)
VALUES (
           '{tenantId}',
           'deposit_notification',
           'Thông báo nạp tiền (User)',
           'fa-money-bill-wave',
           'Nạp tiền thành công - {domain}',
           E'<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
<div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
<div style="text-align: center; margin-bottom: 30px;">
<h1 style="color: #28a745; margin: 0; font-size: 28px;">💰 Nạp tiền thành công!</h1>
</div>
<p style="font-size: 16px; color: #333; margin-bottom: 20px;">Xin chào <strong>{username}</strong>,</p>
<p style="font-size: 16px; color: #333; margin-bottom: 25px;">Chúng tôi xin thông báo giao dịch nạp tiền của bạn đã được xử lý thành công. Số tiền đã được cộng vào tài khoản của bạn và có thể sử dụng ngay bây giờ.</p>
<div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 25px;">
<h3 style="color: #495057; margin-top: 0; margin-bottom: 15px;">📋 Thông tin giao dịch:</h3>
<table style="width: 100%; border-collapse: collapse;">
<tbody>
<tr>
<td style="padding: 8px 0; color: #666;"><strong>Mã giao dịch:</strong></td>
<td style="padding: 8px 0; color: #333;">{transaction_id}</td>
</tr>
<tr>
<td style="padding: 8px 0; color: #666;"><strong>Số tiền:</strong></td>
<td style="padding: 8px 0; color: #28a745; font-weight: bold;">{amount} {currency}</td>
</tr>
<tr>
<td style="padding: 8px 0; color: #666;"><strong>Phương thức:</strong></td>
<td style="padding: 8px 0; color: #333;">{method}</td>
</tr>
<tr>
<td style="padding: 8px 0; color: #666;"><strong>Thời gian:</strong></td>
<td style="padding: 8px 0; color: #333;">{deposit_time}</td>
</tr>
</tbody>
</table>
</div>
<div style="background-color: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 8px; margin-bottom: 25px;">
<p style="margin: 0; color: #155724; font-size: 14px;">✅ Cảm ơn bạn đã tin tưởng và sử dụng dịch vụ của chúng tôi!</p>
</div>
<p style="font-size: 16px; color: #333; margin-bottom: 30px;">Nếu bạn có bất kỳ thắc mắc nào, vui lòng liên hệ với đội ngũ hỗ trợ của chúng tôi.</p>
<div style="text-align: center; border-top: 1px solid #eee; padding-top: 20px;">
<p style="margin: 0; color: #666; font-size: 14px;">Trân trọng,</p>
<p style="margin: 5px 0 0 0; color: #333; font-weight: bold;">Đội ngũ hỗ trợ {domain}</p>
<p style="margin: 5px 0 0 0; color: #666; font-size: 14px;">Email: {support_email}</p>
</div>
</div>
</div>',
           true,
           CURRENT_TIMESTAMP,
           CURRENT_TIMESTAMP,
           '{username},{email},{amount},{currency},{method},{transaction_id},{deposit_time},{domain},{support_email}'
       );

-- Login Notification Template
INSERT INTO public.email_templates (tenant_id, type, name, icon, subject, content, enabled, created_at, updated_at, variables)
VALUES (
           '{tenantId}',
           'login_notification',
           'Thông báo đăng nhập (User)',
           'fa-sign-in-alt',
           'Đăng nhập trên thiết bị khác- {domain}',
           E'<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
<div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
<div style="text-align: center; margin-bottom: 30px;">
<h1 style="color: #dc3545; margin: 0; font-size: 28px;">🚨 Cảnh báo bảo mật!</h1>
</div>
<p style="font-size: 16px; color: #333; margin-bottom: 20px;">Xin chào <strong>{username}</strong>,</p>
<p style="font-size: 16px; color: #333; margin-bottom: 25px;">Chúng tôi phát hiện tài khoản của bạn <strong>({email})</strong> vừa được đăng nhập từ một địa chỉ IP khác.</p>
<div style="background-color: #f8d7da; padding: 20px; border-radius: 8px; margin-bottom: 25px; border-left: 4px solid #dc3545;">
<h3 style="color: #721c24; margin-top: 0; margin-bottom: 15px;">📋 Thông tin đăng nhập:</h3>
<table style="width: 100%; border-collapse: collapse;">
<tbody>
<tr>
<td style="padding: 8px 10px 8px 0; color: #721c24; white-space: nowrap;"><strong>Thời gian:</strong></td>
<td style="padding: 8px 0 8px 10px; color: #721c24;">{login_time}</td>
</tr>
<tr>
<td style="padding: 8px 10px 8px 0; color: #721c24; white-space: nowrap;"><strong>Địa chỉ IP:</strong></td>
<td style="padding: 8px 0 8px 10px; color: #721c24;">{ip_address}</td>
</tr>
<tr>
<td style="padding: 8px 10px 8px 0; color: #721c24; white-space: nowrap;"><strong>Thiết bị:</strong></td>
<td style="padding: 8px 0 8px 10px; color: #721c24; word-break: break-all;">{user_agent}</td>
</tr>
</tbody>
</table>
</div>
<div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 8px; margin-bottom: 25px;">
<h4 style="color: #856404; margin-top: 0; margin-bottom: 10px;">⚠️ Nếu đây là bạn:</h4>
<p style="margin: 0; color: #856404; font-size: 14px;">Bạn có thể bỏ qua email này. Tuy nhiên, chúng tôi khuyến nghị bạn thường xuyên kiểm tra hoạt động đăng nhập.</p>
</div>
<div style="background-color: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 8px; margin-bottom: 25px;">
<h4 style="color: #721c24; margin-top: 0; margin-bottom: 10px;">🔒 Nếu đây KHÔNG phải là bạn:</h4>
<p style="margin: 0 0 10px 0; color: #721c24; font-size: 14px;">Tài khoản của bạn có thể đã bị xâm phạm. Hãy thực hiện ngay:</p>
<ul style="margin: 0; padding-left: 20px; color: #721c24; font-size: 14px;">
<li>Đổi mật khẩu ngay lập tức</li>
<li>Kiểm tra và cập nhật thông tin bảo mật</li>
<li>Đăng xuất khỏi tất cả thiết bị</li>
<li>Liên hệ với chúng tôi ngay lập tức</li>
</ul>
</div>
<div style="text-align: center; margin-bottom: 25px;"><a style="display: inline-block; background-color: #dc3545; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold;" href="{secure_account_url}">🔐 Bảo vệ tài khoản ngay</a></div>
<div style="text-align: center; border-top: 1px solid #eee; padding-top: 20px;">
<p style="margin: 0; color: #666; font-size: 14px;">Đội ngũ bảo mật {domain}</p>
<p style="margin: 5px 0 0 0; color: #666; font-size: 14px;">Email hỗ trợ: {support_email}</p>
</div>
<div style="margin-top: 20px; padding-top: 15px; border-top: 1px solid #eee;">
<p style="margin: 0; color: #999; font-size: 12px; text-align: center;">Email này được gửi tự động từ hệ thống bảo mật của chúng tôi. Vui lòng không trả lời trực tiếp email này.</p>
</div>
</div>
</div>',
           true,
           CURRENT_TIMESTAMP,
           CURRENT_TIMESTAMP,
           '{username},{email},{login_time},{ip_address},{user_agent},{domain},{support_email},{secure_account_url}, {support_phone}'
       );

-- Ticket Reply Notification Template
INSERT INTO public.email_templates (tenant_id, type, name, icon, subject, content, enabled, created_at, updated_at, variables)
VALUES (
           '{tenantId}',
           'ticket_reply_notification',
           'Ticket hỗ trợ (User)',
           'fa-ticket-alt',
           'Ticket hỗ trợ - {domain}',
           E'<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
<div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
<div style="text-align: center; margin-bottom: 30px;">
<h1 style="color: #007bff; margin: 0; font-size: 28px;">🎫 Ticket hỗ trợ</h1>
</div>
<p style="font-size: 16px; color: #333; margin-bottom: 20px;">Xin chào <strong>{username}</strong>,</p>
<p style="font-size: 16px; color: #333; margin-bottom: 25px;">Ticket hỗ trợ của bạn đã được cập nhật.</p>
<div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 25px;">
<h3 style="color: #495057; margin-top: 0; margin-bottom: 15px;">📋 Thông tin ticket:</h3>
<table style="width: 100%; border-collapse: collapse;">
<tbody>
<tr>
<td style="padding: 8px 0; color: #666;"><strong>Mã ticket:</strong></td>
<td style="padding: 8px 0; color: #333;">{ticket_id}</td>
</tr>
<tr>
<td style="padding: 8px 0; color: #666;"><strong>Tiêu đề:</strong></td>
<td style="padding: 8px 0; color: #333;">{subject}</td>
</tr>
<tr>
<td style="padding: 8px 0; color: #666;"><strong>Trạng thái:</strong></td>
<td style="padding: 8px 0; color: #333;">{status}</td>
</tr>
<tr>
<td style="padding: 8px 0; color: #666;"><strong>Thời gian tạo:</strong></td>
<td style="padding: 8px 0; color: #333;">{created_time}</td>
</tr>
</tbody>
</table>
</div>
<div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 20px; border-radius: 8px; margin-bottom: 25px;">
<h3 style="color: #856404; margin-top: 0; margin-bottom: 15px;">💬 Nội dung cập nhật:</h3>
<div style="color: #333; font-size: 14px; line-height: 1.6; white-space: pre-wrap;">{message}</div>
</div>
<div style="background-color: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; border-radius: 8px; margin-bottom: 25px;">
<p style="margin: 0; color: #0c5460; font-size: 14px;">📝 Chúng tôi sẽ phản hồi bạn trong thời gian sớm nhất.</p>
</div>
<div style="text-align: center; border-top: 1px solid #eee; padding-top: 20px;">
<p style="margin: 0; color: #666; font-size: 14px;">Trân trọng,</p>
<p style="margin: 5px 0 0 0; color: #333; font-weight: bold;">Đội ngũ hỗ trợ {domain}</p>
<p style="margin: 5px 0 0 0; color: #666; font-size: 14px;">Email: {support_email}</p>
</div>
</div>
</div>',
           true,
           CURRENT_TIMESTAMP,
           CURRENT_TIMESTAMP,
           '{username},{email},{ticket_id},{subject},{status},{message},{created_time},{domain},{support_email}'
       );

-- 2. INSERT DEFAULT TELEGRAM TEMPLATES
-- =====================================================

-- Provider Balance Warning Template
INSERT INTO public.telegram_templates (tenant_id, type, name, icon, content, enabled, created_at, updated_at, variables, addition_setting, sort_order)
VALUES (
           '{tenantId}',
           'provider_balance_warning',
           'Thông báo hết tiền nhà cung cấp (Admin)',
           'fa-wallet',
           E'⚠️ <b>CẢNH BÁO: HẾT TIỀN NHÀ CUNG CẤP</b>

🏪 <b>Nhà cung cấp:</b> {supplier}
💰 <b>Số dư hiện tại:</b> {money}
⏰ <b>Thời gian:</b> {time}

❗ <i>Cần nạp tiền ngay để tránh gián đoạn dịch vụ</i>',
           true,
           CURRENT_TIMESTAMP,
           CURRENT_TIMESTAMP,
           '{provider_name},{current_balance},{required_amount},{order_id},{service_name},{quantity},{time}',
           null,
           70
       );

-- New Service Notification Template
INSERT INTO public.telegram_templates (tenant_id, type, name, icon, content, enabled, created_at, updated_at, variables, addition_setting, sort_order)
VALUES (
           '{tenantId}',
           'new_service_notification',
           'Thông báo service mới (User)',
           'fa-bullhorn',
           E'🚀 <b>DỊCH VỤ MỚI ĐÃ RA MẮT</b>

🌐 <b>Tại:</b> {domain}
🛍️ <b>Dịch vụ:</b> {new_service}
💰 <b>Giá:</b> {price_new_service}

🎉 <i>Hãy trải nghiệm dịch vụ mới ngay hôm nay!</i>',
           true,
           CURRENT_TIMESTAMP,
           CURRENT_TIMESTAMP,
           '{domain},{new_service},{price_new_service}',
           null,
           110
       );

-- Deposit Notification Template for Admin
INSERT INTO public.telegram_templates (tenant_id, type, name, icon, content, enabled, created_at, updated_at, variables, addition_setting, sort_order)
VALUES (
           '{tenantId}',
           'deposit_notification',
           'Thông báo nạp tiền (Admin)',
           'fa-money-bill-wave',
           E'💳 <b>GIAO DỊCH NẠP TIỀN MỚI</b>

👤 <b>Người dùng:</b> {username}
💳 <b>Phương thức:</b> {method}
🆔 <b>Mã giao dịch:</b> {trans_id}
💰 <b>Số tiền:</b> {amount}
⏰ <b>Thời gian:</b> {time}

✅ <i>Giao dịch đang chờ xử lý</i>',
           true,
           CURRENT_TIMESTAMP,
           CURRENT_TIMESTAMP,
           null,
           null,
           30
       );

-- Reply Ticket Notification Template
INSERT INTO public.telegram_templates (tenant_id, type, name, icon, content, enabled, created_at, updated_at, variables, addition_setting, sort_order)
VALUES (
           '{tenantId}',
           'reply_ticket_notification',
           'User trả lời ticket (Admin)',
           'fa-ticket-alt',
           E'🎫 <b>USER TRẢ LỜI TICKET</b>

👤 <b>Người dùng:</b> {username}
🆔 <b>Mã ticket:</b> #{ticket_id}
📋 <b>Chủ đề:</b> {subject}
💬 <b>Nội dung:</b>
<i>{message}</i>

⏰ <b>Thời gian:</b> {time}

💡 <i>Cần phản hồi từ admin</i>',
           true,
           CURRENT_TIMESTAMP,
           CURRENT_TIMESTAMP,
           null,
           null,
           50
       );

-- API Order Notification Template
INSERT INTO public.telegram_templates (tenant_id, type, name, icon, content, enabled, created_at, updated_at, variables, addition_setting, sort_order)
VALUES (
           '{tenantId}',
           'api_order_notification',
           'Đơn hàng API (Admin)',
           'fa-robot',
           E'🤖 <b>ĐƠN HÀNG API MỚI</b>

👤 <b>Người dùng:</b> {username}
🛍️ <b>Dịch vụ:</b> {service}
🏪 <b>Nhà cung cấp:</b> {supplier}
💰 <b>Số tiền:</b> {pay}
🔗 <b>Link:</b> {link}
📊 <b>Số lượng:</b> {quantity}
⏰ <b>Thời gian:</b> {time}

<i>Đơn hàng được tạo tự động qua API</i>',
           true,
           CURRENT_TIMESTAMP,
           CURRENT_TIMESTAMP,
           E'{supplier},{username},{order_id},{service},{quantity},{pay},{ip},{time},{link},{domain}',
           null,
           10
       );

-- Manual Order Notification Template
INSERT INTO public.telegram_templates (tenant_id, type, name, icon, content, enabled, created_at, updated_at, variables, addition_setting, sort_order)
VALUES (
           '{tenantId}',
           'manual_order_notification',
           'Đơn hàng thủ công (Admin)',
           'fa-hand-paper',
           E'✋ <b>ĐƠN HÀNG THỦ CÔNG MỚI</b>

👤 <b>Người dùng:</b> {username}
🛍️ <b>Dịch vụ:</b> {service}
💰 <b>Số tiền:</b> {pay}
🔗 <b>Link:</b> {link}
📊 <b>Số lượng:</b> {quantity}
⏰ <b>Thời gian:</b> {time}

<i>Đơn hàng được tạo thủ công bởi admin</i>',
           true,
           CURRENT_TIMESTAMP,
           CURRENT_TIMESTAMP,
           '{username},{order_id},{service},{quantity},{pay},{ip},{time},{link},{domain}',
           '{"selected_services":[1102,1079],"is_all_service":false}',
           20
       );

-- Provider Insufficient Balance Template
INSERT INTO public.telegram_templates (tenant_id, type, name, icon, content, enabled, created_at, updated_at, variables, addition_setting, sort_order)
VALUES (
           '{tenantId}',
           'provider_insufficient_balance',
           'Không đủ số dư nhà cung cấp khi order (Admin)',
           'fa-exclamation-triangle',
           E'⚠️ *Nhà cung cấp hết tiền*

     🏢 Nhà cung cấp: {provider_name}
     💰 Số dư hiện tại: ${current_balance}
     💸 Số tiền cần: ${required_amount}
     🆔 Mã đơn hàng: {order_id}
     🎯 Dịch vụ: {service_name}
     📊 Số lượng: {quantity}
     ⏰ Thời gian: {time}

     _Vui lòng nạp tiền cho nhà cung cấp để tiếp tục xử lý đơn hàng._',
           true,
           CURRENT_TIMESTAMP,
           CURRENT_TIMESTAMP,
           '{provider_name},{current_balance},{required_amount},{order_id},{service_name},{quantity},{time}',
           null,
           90
       );

-- Child Panel Request Notification Template
INSERT INTO public.telegram_templates (tenant_id, type, name, icon, content, enabled, created_at, updated_at, variables, addition_setting, sort_order)
VALUES (
           '{tenantId}',
           'child_panel_request_notification',
           'Yêu cầu panel con (Admin)',
           'fa-users',
           E'👥 <b>YÊU CẦU PANEL CON MỚI</b>

👤 <b>Người yêu cầu:</b> {username}
📧 <b>Email:</b> {email}
🌐 <b>Domain:</b> {domain}
📝 <b>Ghi chú:</b> {note}
⏰ <b>Thời gian:</b> {request_time}

⚠️ <i>Vui lòng xem xét và phê duyệt yêu cầu này</i>',
           true,
           CURRENT_TIMESTAMP,
           CURRENT_TIMESTAMP,
           '{domain},{username},{bank},{account_number},{account_name},{amount},{price},{id},{time}',
           null,
           60
       );

-- Admin Reply Ticket Notification Template
INSERT INTO public.telegram_templates (tenant_id, type, name, icon, content, enabled, created_at, updated_at, variables, addition_setting, sort_order)
VALUES (
           '{tenantId}',
           'admin_reply_ticket_notification',
           'Thông báo admin reply ticket (User)',
           'fa-ticket-alt',
           E'📩 <b>PHẢN HỒI TỪ BỘ PHẬN HỖ TRỢ</b>

👋 <b>Kính chào:</b> {username}
🎫 <b>Ticket:</b> #{ticket_id}
📋 <b>Chủ đề:</b> {subject}

💬 <b>Nội dung phản hồi:</b>
<i>{message}</i>

⏰ <b>Thời gian:</b> {time}

📞 <i>Nếu cần hỗ trợ thêm, vui lòng liên hệ lại</i>',
           true,
           CURRENT_TIMESTAMP,
           CURRENT_TIMESTAMP,
           null,
           null,
           120
       );

-- Ticket Notification Template
INSERT INTO public.telegram_templates (tenant_id, type, name, icon, content, enabled, created_at, updated_at, variables, addition_setting, sort_order)
VALUES (
           '{tenantId}',
           'ticket_notification',
           'Ticket hỗ trợ (Admin)',
           'fa-ticket-alt',
           E'🎫 <b>TICKET HỖ TRỢ MỚI</b>

👤 <b>Người dùng:</b> {username}
🆔 <b>Mã ticket:</b> #{ticket_id}
📋 <b>Chủ đề:</b> {subject}
💬 <b>Nội dung:</b>
<i>{message}</i>

⏰ <b>Thời gian tạo:</b> {time}

🔔 <i>Ticket mới cần được xử lý</i>',
           true,
           CURRENT_TIMESTAMP,
           CURRENT_TIMESTAMP,
           '{subject},{message},{ticket_id},{time},{username}',
           null,
           40
       );

-- 3. UPDATE TENANT SETTINGS
-- =====================================================

-- Update tenant languages and currencies
UPDATE public.tenant
SET
    available_languages = 'en,vi,ru,tr,pt-br,ar,ko,es,th,fr,cn,de,id,it,ja,pl,uk,fa,bn,hi,pt,zh',
    available_currencies = 'USD,VND,RUB,TRY,BRL,KRW,SAR,AED,PLN,UAH,IRR,IDR,BDT,INR,THB,CNY,EUR,JPY'
WHERE id = '{tenantId}';