package tndung.vnfb.smm.repository.tenant;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import tndung.vnfb.smm.constant.enums.TransactionType;
import tndung.vnfb.smm.entity.GOrder;
import tndung.vnfb.smm.entity.GTransaction;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.Optional;

public interface TransactionRepository extends TenantAwareRepository<GTransaction, Long> {
    @Query("SELECT t FROM GTransaction t WHERE t.order = :order AND t.type = :type AND t.tenantId = :#{T(tndung.vnfb.smm.config.TenantContext).getWildcardTenant()}")
    Optional<GTransaction> findByOrderAndType(@Param("order") GOrder order, @Param("type") TransactionType type);

    @Query("SELECT t FROM GTransaction t WHERE t.order = :order AND t.type = :type AND t.tenantId = :#{T(tndung.vnfb.smm.config.TenantContext).getWildcardTenant()} ORDER BY t.createdAt ASC")
    List<GTransaction> findAllByOrderAndType(@Param("order") GOrder order, @Param("type") TransactionType type);

    @Query("SELECT t FROM GTransaction t WHERE t.order = :order AND t.type IN :types AND t.tenantId = :#{T(tndung.vnfb.smm.config.TenantContext).getWildcardTenant()} ORDER BY t.createdAt ASC")
    List<GTransaction> findAllByOrderAndTypeIn(@Param("order") GOrder order, @Param("types") List<TransactionType> types);

    @Query("SELECT SUM(t.change) FROM GTransaction t WHERE t.type = 'Deposit' AND t.createdAt BETWEEN :startDate AND :endDate AND t.tenantId = :tenantId")
    BigDecimal sumRevenueBetweenDates(@Param("startDate") OffsetDateTime startDate, @Param("endDate") OffsetDateTime endDate, @Param("tenantId") String tenantId);

    @Query("SELECT t FROM GTransaction t WHERE t.userId = :userId AND t.tenantId = :#{T(tndung.vnfb.smm.config.TenantContext).getWildcardTenant()} ORDER BY t.createdAt DESC, t.id DESC")
    List<GTransaction> findTopByUserIdOrderByCreatedAtDesc(@Param("userId") Long userId, Pageable pageable);

    // Alternative method to get the latest transaction safely
    @Query(value = "SELECT * FROM g_transaction WHERE user_id = :userId AND tenant_id = :#{T(tndung.vnfb.smm.config.TenantContext).getWildcardTenant()} ORDER BY created_at DESC, id DESC LIMIT 1", nativeQuery = true)
    Optional<GTransaction> findLatestByUserId(@Param("userId") Long userId);
    /**
     * Calculate revenue for a specific tenant ID
     */
    @Query("SELECT COALESCE(SUM(t.change), 0) FROM GTransaction t WHERE t.tenantId = :tenantId AND t.type = 'Deposit' AND t.createdAt >= :startDate AND t.createdAt <= :endDate")
    BigDecimal calculateRevenueByTenantId(@Param("tenantId") String tenantId, @Param("startDate") OffsetDateTime startDate, @Param("endDate") OffsetDateTime endDate);

    /**
     * Check if transaction with external transaction ID already exists
     */
    @Query("SELECT COUNT(t) > 0 FROM GTransaction t WHERE t.externalTransactionId = :externalTransactionId AND t.tenantId = :#{T(tndung.vnfb.smm.config.TenantContext).getWildcardTenant()}")
    boolean existsByExternalTransactionId(@Param("externalTransactionId") String externalTransactionId);

    /**
     * Calculate revenue for a specific tenant ID (default to last year)
     */
    default BigDecimal calculateRevenueByTenantId(String tenantId) {
        return calculateRevenueByTenantId(tenantId, OffsetDateTime.now().minusYears(1), OffsetDateTime.now());
    }

    // Dashboard V2 queries
    @Query(value = "SELECT COALESCE(SUM(t.change), 0) FROM g_transaction t WHERE t.type = 'Deposit' AND t.tenant_id = :tenantId", nativeQuery = true)
    BigDecimal getTotalDeposits(@Param("tenantId") String tenantId);

    @Query(value = "SELECT COALESCE(SUM(ABS(t.change)), 0) FROM g_transaction t WHERE t.type = 'Spent' AND t.tenant_id = :tenantId", nativeQuery = true)
    BigDecimal getTotalSpending(@Param("tenantId") String tenantId);

    @Query(value = "SELECT COALESCE(SUM(t.balance), 0) FROM g_transaction t WHERE t.id IN " +
            "(SELECT MAX(t2.id) FROM g_transaction t2 WHERE t2.tenant_id = :tenantId GROUP BY t2.user_id)", nativeQuery = true)
    BigDecimal getTotalUserBalance(@Param("tenantId") String tenantId);

    @Query(value = "SELECT t.created_at as date, COALESCE(SUM(t.change), 0) as value " +
            "FROM g_transaction t WHERE t.type = 'Deposit' " +
            "AND t.created_at BETWEEN :startDate AND :endDate " +
            "AND t.tenant_id = :tenantId " +
            "GROUP BY t.created_at ORDER BY t.created_at", nativeQuery = true)
    List<Object[]> getDailyDepositStats(@Param("startDate") OffsetDateTime startDate, @Param("endDate") OffsetDateTime endDate, @Param("tenantId") String tenantId);

    @Query(value = "SELECT COALESCE(SUM(t.change), 0) FROM g_transaction t WHERE t.type = 'Deposit' " +
            "AND t.created_at BETWEEN :startDate AND :endDate " +
            "AND t.tenant_id = :tenantId", nativeQuery = true)
    BigDecimal getTodayDeposits(@Param("startDate") OffsetDateTime startDate, @Param("endDate") OffsetDateTime endDate, @Param("tenantId") String tenantId);

    @Query(value = "SELECT COALESCE(SUM(ABS(t.change)), 0) FROM g_transaction t WHERE t.type = 'Spent' " +
            "AND t.created_at BETWEEN :startDate AND :endDate " +
            "AND t.tenant_id = :tenantId", nativeQuery = true)
    BigDecimal getTodaySpending(@Param("startDate") OffsetDateTime startDate, @Param("endDate") OffsetDateTime endDate, @Param("tenantId") String tenantId);

    /**
     * Calculate total deposit amount for a specific user (using original amount, not including bonus)
     */
    @Query("SELECT COALESCE(SUM(CASE WHEN t.originalAmount IS NOT NULL THEN t.originalAmount ELSE t.change END), 0) FROM GTransaction t WHERE t.userId = :userId AND t.type = 'Deposit' AND t.tenantId = :tenantId")
    BigDecimal getTotalDepositByUserId(@Param("userId") Long userId, @Param("tenantId") String tenantId);

    // Bonus-related queries for dashboard
    @Query(value = "SELECT COALESCE(SUM(t.change), 0) FROM g_transaction t WHERE t.type = 'Bonus' AND t.tenant_id = :tenantId", nativeQuery = true)
    BigDecimal getTotalBonus(@Param("tenantId") String tenantId);

    @Query(value = "SELECT COALESCE(SUM(t.change), 0) FROM g_transaction t WHERE t.type = 'Bonus' " +
            "AND t.created_at BETWEEN :startDate AND :endDate " +
            "AND t.tenant_id = :tenantId", nativeQuery = true)
    BigDecimal getTodayBonus(@Param("startDate") OffsetDateTime startDate, @Param("endDate") OffsetDateTime endDate, @Param("tenantId") String tenantId);

    @Query(value = "SELECT t.* FROM g_transaction t " +
            "LEFT JOIN g_user u ON t.user_id = u.id " +
            "LEFT JOIN payment_method pm ON t.payment_method_id = pm.id " +
            "WHERE t.tenant_id = :tenantId " +
            "AND (:username IS NULL OR u.user_name = :username) " +
            "AND (:paymentMethodId IS NULL OR t.payment_method_id = :paymentMethodId) " +
            "AND (:isBonusFilter IS FALSE OR t.type = 'Bonus') " +
            "AND (t.type IN :types) " +
            "AND t.created_at BETWEEN :startDate AND :endDate " +
            "ORDER BY t.created_at DESC",
            countQuery = "SELECT COUNT(t.id) FROM g_transaction t " +
                    "LEFT JOIN g_user u ON t.user_id = u.id " +
                    "WHERE t.tenant_id = :tenantId " +
                    "AND (:username IS NULL OR u.user_name = :username) " +
                    "AND (:paymentMethodId IS NULL OR t.payment_method_id = :paymentMethodId) " +
                    "AND (t.type IN :types) " +
                    "AND t.created_at BETWEEN :startDate AND :endDate",
            nativeQuery = true)
    Page<GTransaction> findTransactionsWithFilters(
            @Param("username") String username,
            @Param("paymentMethodId") Long paymentMethodId,
            @Param("isBonusFilter") boolean isBonusFilter,
            @Param("types") List<String> types,
            @Param("startDate") OffsetDateTime startDate,
            @Param("endDate") OffsetDateTime endDate,
            @Param("tenantId") String tenantId,
            Pageable pageable);

    // Repository Method
    @Query(value = "SELECT " +
            "t.id, " +
            "t.user_id, " +
            "u.user_name as username, " +
            "t.note, " +
            "t.order_id, " +
            "t.change, " +
            "t.balance, " +
            "t.original_amount, " +
            "t.type, " +
            "t.source, " +
            "t.payment_method_id, " +
            "pm.name as payment_method_name, " +
            "t.external_transaction_id, " +
            "t.created_at, " +
            "COUNT(*) OVER() as total_count " +
            "FROM g_transaction t " +
            "LEFT JOIN g_user u ON t.user_id = u.id " +
            "LEFT JOIN payment_method pm ON t.payment_method_id = pm.id " +
            "WHERE t.tenant_id = :tenantId " +
            "AND (:username IS NULL OR LOWER(u.user_name) LIKE LOWER(CONCAT('%', :username, '%'))) " +
            "AND (:paymentMethodId IS NULL OR t.payment_method_id = :paymentMethodId) " +
            "AND (:isBonusFilter IS FALSE OR t.type = 'Bonus') " +
            "AND (:isRemoveFilter IS FALSE OR t.type = 'Remove') " +
            "AND (t.type IN :types) " +
            "AND t.created_at BETWEEN :startDate AND :endDate " +
            "ORDER BY t.created_at DESC " +
            "LIMIT :#{#pageable.pageSize} OFFSET :#{#pageable.offset}",
            nativeQuery = true)
    List<Object[]> findTransactionsWithFiltersSingleQuery(
            @Param("username") String username,
            @Param("paymentMethodId") Long paymentMethodId,
            @Param("isBonusFilter") boolean isBonusFilter,
            @Param("isRemoveFilter") boolean isRemoveFilter,
            @Param("types") List<String> types,
            @Param("startDate") OffsetDateTime startDate,
            @Param("endDate") OffsetDateTime endDate,
            @Param("tenantId") String tenantId,
            Pageable pageable);
}
