import { Component, OnInit, Inject, PLATFORM_ID } from '@angular/core';
import { CommonModule, isPlatformBrowser } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { UserService } from '../../core/services/user.service';
import { ToastService } from '../../core/services/toast.service';
import { AuthService } from '../../core/services/auth.service';

@Component({
  selector: 'app-email-verification',
  standalone: true,
  imports: [CommonModule, TranslateModule],
  templateUrl: './email-verification.component.html',
  styleUrls: ['./email-verification.component.css']
})
export class EmailVerificationComponent implements OnInit {
  isLoading = true;
  isSuccess = false;
  hasError = false;
  errorMessage = '';
  token = '';

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private userService: UserService,
    private toastService: ToastService,
    public authService: AuthService,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {
    // Debug: Log initial state

  }

  ngOnInit(): void {
    if (!isPlatformBrowser(this.platformId)) {
      return;
    }
    this.route.queryParams.subscribe(params => {
      console.log('Query params received:', params);
      this.token = params['token'];
      
      if (this.token) {
        console.log('Token found, calling verifyEmail');
        this.verifyEmail();
      } else {
        console.log('No token found, setting error state');
        this.setErrorState('Invalid verification link');
      }
    });
  }

  private verifyEmail(): void {
    console.log('verifyEmail called, resetting states');
    this.resetStates();
    this.isLoading = true;
    
    console.log('States after reset:', {
      isLoading: this.isLoading,
      isSuccess: this.isSuccess,
      hasError: this.hasError
    });
    
    // Call the backend API to verify email
    this.userService.confirmEmailVerification(this.token).subscribe({
      next: (response) => {
        console.log('API success response:', response);
        this.setSuccessState();
        this.toastService.showSuccess('Email verified successfully');
        
        // If user is logged in, refresh their data but don't auto-redirect
        if (this.authService.isAuthenticated()) {
          this.userService.getMe().subscribe({
            next: () => {
              console.log('Email verified and user data refreshed');
            },
            error: (err) => {
              console.error('Failed to refresh user data after email verification', err);
            }
          });
        }
      },
      error: (error) => {
        console.log('API error response:', error);
        this.setErrorState(error.message || 'Verification failed');
        this.toastService.showError(error.message || 'Verification failed');
      }
    });
  }

  private resetStates(): void {
    console.log('Resetting all states');
    this.isLoading = false;
    this.isSuccess = false;
    this.hasError = false;
    this.errorMessage = '';
  }

  private setSuccessState(): void {
    console.log('Setting success state');
    this.isLoading = false;
    this.isSuccess = true;
    this.hasError = false;
    this.errorMessage = '';
    
    console.log('Success state set:', {
      isLoading: this.isLoading,
      isSuccess: this.isSuccess,
      hasError: this.hasError
    });
  }

  private setErrorState(message: string): void {
    console.log('Setting error state with message:', message);
    this.isLoading = false;
    this.isSuccess = false;
    this.hasError = true;
    this.errorMessage = message;
    
    console.log('Error state set:', {
      isLoading: this.isLoading,
      isSuccess: this.isSuccess,
      hasError: this.hasError,
      errorMessage: this.errorMessage
    });
  }

  goToLogin(): void {
    // if (this.authService.isAuthenticated()) {
    //   this.router.navigate(['/dashboard/settings']);
    // } else {
      this.router.navigate(['/auth/login']);
   //}
  }

  goToHome(): void {
    this.router.navigate(['/']);
  }
}