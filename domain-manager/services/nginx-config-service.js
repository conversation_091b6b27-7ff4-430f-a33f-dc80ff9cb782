/**
 * Nginx configuration generation service
 */

const config = require('../config/constants');
const logger = require('../utils/logger');
const FileUtils = require('../utils/file-utils');

class NginxConfigService {
  /**
   * Generate Nginx configuration for a tenant
   * @param {Object} tenant - Tenant object
   * @param {boolean} httpsOnly - Generate only HTTPS configuration
   */
  static async generateConfig(tenant, httpsOnly = false) {
    try {
      if (!tenant) {
        throw new Error('No tenant provided to generateConfig');
      }

      logger.info(`Generating Nginx configuration for domain: ${tenant.domain}`);

      // Ensure conf.d directory exists
      FileUtils.ensureDirectoryExists(config.NGINX_CONF_DIR);

      // Create safe filename
      const safeFileName = FileUtils.createSafeFileName(tenant.domain);
      const configPath = `${config.NGINX_CONF_DIR}/tenant-${safeFileName}.conf`;

      // Check SSL certificate availability
      const sslCertExists = this._checkSSLCertificates(tenant);

      // Skip HTTPS-only generation if no certificates
      if (httpsOnly && !sslCertExists) {
        logger.warn(`Skipping HTTPS config for ${tenant.domain} - no SSL certificates found`);
        return;
      }

      // Generate configuration content
      const configContent = this._generateConfigContent(tenant, httpsOnly, sslCertExists);

      // Write configuration file
      if (this._hasValidContent(configContent, tenant.domain)) {
        if (httpsOnly && FileUtils.fileExists(configPath)) {
          FileUtils.appendFile(configPath, configContent, `HTTPS configuration for ${tenant.domain}`);
        } else {
          FileUtils.writeFile(configPath, configContent, `Nginx configuration for ${tenant.domain}`);
        }
      }

      // Reload Nginx
      await this._reloadNginx();

    } catch (error) {
      logger.error(`Error generating Nginx config: ${error.message}`, { tenant: tenant.domain });
      throw error;
    }
  }

  /**
   * Generate Nginx configuration for a child panel tenant
   * @param {Object} tenant - Tenant object
   * @param {boolean} httpsOnly - Generate only HTTPS configuration
   */
  static async generateChildPanelConfig(tenant, httpsOnly = false) {
    try {
      if (!tenant) {
        throw new Error('No tenant provided to generateChildPanelConfig');
      }

      logger.info(`Generating Child Panel Nginx configuration for domain: ${tenant.domain}`);

      // Ensure conf.d directory exists
      FileUtils.ensureDirectoryExists(config.NGINX_CONF_DIR);

      // Create safe filename
      const safeFileName = FileUtils.createSafeFileName(tenant.domain);
      const configPath = `${config.NGINX_CONF_DIR}/tenant-${safeFileName}.conf`;

      // Check SSL certificate availability
      const sslCertExists = this._checkSSLCertificates(tenant);

      // Skip HTTPS-only generation if no certificates
      if (httpsOnly && !sslCertExists) {
        logger.warn(`Skipping HTTPS config for ${tenant.domain} - no SSL certificates found`);
        return;
      }

      // Generate configuration content for child panel
      const configContent = this._generateChildPanelConfigContent(tenant, httpsOnly, sslCertExists);

      // Write configuration file
      if (this._hasValidContent(configContent, tenant.domain)) {
        if (httpsOnly && FileUtils.fileExists(configPath)) {
          FileUtils.appendFile(configPath, configContent, `Child Panel HTTPS configuration for ${tenant.domain}`);
        } else {
          FileUtils.writeFile(configPath, configContent, `Child Panel Nginx configuration for ${tenant.domain}`);
        }
      }

      // Reload Nginx
      await this._reloadNginx();

    } catch (error) {
      logger.error(`Error generating Child Panel Nginx config: ${error.message}`, { tenant: tenant.domain });
      throw error;
    }
  }

  /**
   * Check if SSL certificates exist for the tenant
   * @param {Object} tenant - Tenant object
   * @returns {boolean} True if certificates exist
   */
  static _checkSSLCertificates(tenant) {
    const certPath = `${config.SSL_PATH}/${tenant.domain}.crt`;
    const keyPath = `${config.SSL_PATH}/${tenant.domain}.key`;
    return FileUtils.filesExist([certPath, keyPath]);
  }

  /**
   * Generate configuration content
   * @param {Object} tenant - Tenant object
   * @param {boolean} httpsOnly - Generate only HTTPS configuration
   * @param {boolean} sslCertExists - Do SSL certificates exist
   * @returns {string} Configuration content
   */
  static _generateConfigContent(tenant, httpsOnly, sslCertExists) {
    const serverName = tenant.domain;
    const apiUrl = tenant.api_url || config.SMM_SYSTEM_URL;

    let configContent = `# Tenant configuration for ${tenant.domain} - DO NOT EDIT MANUALLY\n\n`;

    // Add HTTP server block
    if (!httpsOnly) {
      configContent += this._generateHTTPBlock(serverName);
    }

    // Add HTTPS server block
    if (sslCertExists) {
      configContent += this._generateHTTPSBlock(tenant, serverName, apiUrl);
    }

    return configContent;
  }

  /**
   * Generate child panel configuration content
   * @param {Object} tenant - Tenant object
   * @param {boolean} httpsOnly - Generate only HTTPS configuration
   * @param {boolean} sslCertExists - Do SSL certificates exist
   * @returns {string} Configuration content
   */
  static _generateChildPanelConfigContent(tenant, httpsOnly, sslCertExists) {
    const serverName = tenant.domain;

    let configContent = `# Child Panel Tenant configuration for ${tenant.domain} - DO NOT EDIT MANUALLY\n\n`;

    // Add HTTP server block
    if (!httpsOnly) {
      configContent += this._generateHTTPBlock(serverName);
    }

    // Add HTTPS server block for child panel
    if (sslCertExists) {
      configContent += this._generateChildDomainHTTPSBlock(tenant, serverName);
    }

    return configContent;
  }

  /**
   * Generate HTTP server block
   * @param {string} serverName - Server name
   * @returns {string} HTTP block configuration
   */
  static _generateHTTPBlock(serverName) {
    return `
# HTTP server block for ${serverName} - handles Let's Encrypt challenges
server {
    listen 80;
    listen [::]:80;
    server_name ${serverName};

    # Let's Encrypt challenge response - MUST BE BEFORE THE REDIRECT
    location ~ /.well-known/acme-challenge/ {
        root ${config.CERTBOT_WEBROOT};
    }

    # Redirect all other HTTP requests to HTTPS
    location / {
      return 301 https://$host$request_uri;
    }
}
`;
  }

  /**
   * Generate HTTPS server block
   * @param {Object} tenant - Tenant object
   * @param {string} serverName - Server name
   * @param {string} apiUrl - API URL
   * @returns {string} HTTPS block configuration
   */
  static _generateHTTPSBlock(tenant, serverName, apiUrl) {
    const sslCertPath = this._getSSLCertPath(tenant);
    const sslKeyPath = this._getSSLKeyPath(tenant);

    return `
# HTTPS server block for ${serverName}
server {
    listen 443 ssl;
    listen [::]:443 ssl;
    server_name ${serverName} www.${serverName};

    ssl_certificate ${sslCertPath};
    ssl_certificate_key ${sslKeyPath};

    # Set tenant-specific environment variables
    set $tenant_id "${tenant.id}";
    set $tenant_domain "${tenant.domain}";
    set $tenant_api_url "${apiUrl}";

    # Frontend routing
    location / {
        proxy_pass ${config.SMM_DASHBOARD_URL};
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Tenant-ID "$tenant_id";
        proxy_set_header X-Tenant-Domain "$tenant_domain";
        proxy_set_header X-Tenant-API-URL "$tenant_api_url";

        # Pass tenant-specific configuration to the frontend
        sub_filter '</head>' '<script>
            window.TENANT_CONFIG = {
                tenantId: "${tenant.id}",
                domain: "${tenant.domain}",
                apiUrl: "${apiUrl}",
                theme: "${tenant.theme || 'default'}",
                logoUrl: "${tenant.logo_url || ''}",
                companyName: "${tenant.company_name || 'SMM System'}"
            };
        </script></head>';
        sub_filter_once on;
    }

    # Backend API routing
    location /api/ {
        proxy_pass ${config.SMM_SYSTEM_URL};
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Tenant-ID "$tenant_id";
        proxy_set_header X-Tenant-Domain "$tenant_domain";
    }

    ${this._generateCDNBlock()}
}
`;
  }

  /**
   * Generate HTTPS server block for child domain with admin panel support
   * @param {Object} tenant - Tenant object
   * @param {string} serverName - Server name
   * @returns {string} HTTPS block configuration
   */
  static _generateChildDomainHTTPSBlock(tenant, serverName) {
    const sslCertPath = this._getSSLCertPath(tenant);
    const sslKeyPath = this._getSSLKeyPath(tenant);
    
    return `
# Tenant configuration for ${serverName} - DO NOT EDIT MANUALLY
# HTTPS server block for ${serverName}
server {
    listen 443 ssl;
    listen [::]:443 ssl;
    server_name ${serverName} www.${serverName};
    ssl_certificate ${sslCertPath};
    ssl_certificate_key ${sslKeyPath};
    
    # Set tenant-specific environment variables
    set $tenant_id "${tenant.id}";
    set $tenant_domain "${tenant.domain}";
    set $tenant_api_url "https://${tenant.domain}/api/v1";
    set $tenant_site_url "https://${tenant.domain}";
    
    # Handle static assets for admin panel (i18n, styles, scripts, images)
    location ~* ^/admin/assets/(.*)$ {
        proxy_pass http://smm-admin:4001/assets/$1;
        proxy_set_header Host $host;
        expires 1d;
        add_header Cache-Control "public, immutable";
        
        # Fallback to root assets if not found in panel
        error_page 404 = @fallback_assets_admin;
    }
    
    # Fallback for assets not found in panel path
    location @fallback_assets_admin {
        rewrite ^/admin/assets/(.*)$ /assets/$1 break;
        proxy_pass http://smm-admin:4001;
        proxy_set_header Host $host;
        expires 1d;
    }
    
    # Handle other static files for admin panel (js, css at root level)  
    location ~* ^/admin/.*\\.(js|mjs|css|map)$ {
        rewrite ^/admin/(.*)$ /$1 break;
        proxy_pass http://smm-admin:4001;
        proxy_set_header Host $host;
        expires 1d;
        add_header Cache-Control "public, immutable";
    }
    
    # Redirect admin auth to shared auth flow
    location ~ ^/admin/auth/(.*)$ {
        return 301 /auth/$1;
    }
    
    # Handle i18n files specifically for admin panel with debug
    location ~ ^/admin/assets/i18n/(.*)$ {
        # Try admin service first
        proxy_pass http://smm-admin:4001/assets/i18n/$1;
        proxy_set_header Host $host;
        expires 1d;
        add_header X-Served-By "admin-i18n";
        add_header X-Original-Path "$request_uri";
        
        # If not found, fallback to dashboard service
        proxy_intercept_errors on;
        error_page 404 = @admin_i18n_fallback;
    }
    
    # Fallback for missing admin i18n files - try dashboard service
    location @admin_i18n_fallback {
        rewrite ^/admin/assets/i18n/(.*)$ /assets/i18n/$1 break;
        proxy_pass http://smm-dashboard:4002;
        proxy_set_header Host $host;
        add_header X-Served-By "admin-i18n-fallback-dashboard";
        add_header X-Original-Path "$request_uri";
    }
    
    # Admin panel routing - Use path /admin/ to share data between services
    location /admin/ {
        proxy_pass http://smm-admin:4001/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Tenant-ID "$tenant_id";
        proxy_set_header X-Tenant-Domain "$tenant_domain";
        proxy_set_header X-Tenant-API-URL "$tenant_api_url";
        
        # Tell Angular about the base path for proper routing
        proxy_set_header X-Forwarded-Prefix "/admin";
        proxy_set_header X-Script-Name "/admin";
        
        # Fix base href for Angular and inject tenant config
        sub_filter '<base href="/">' '<base href="/admin/">';
        sub_filter '</head>' '<script>
            window.TENANT_CONFIG = {
                tenantId: "${tenant.id}",
                domain: "${tenant.domain}",
                apiUrl: "https://${tenant.domain}/api/v1",
                theme: "${tenant.theme || 'default'}",
                logoUrl: "${tenant.logo_url || ''}",
                companyName: "${tenant.company_name || 'SMM System'}"
            };
        </script></head>';
        sub_filter_once off;
    }
    
    # Handle /admin without trailing slash - redirect to with slash
    location = /admin {
        return 301 /admin/;
    }
    
    # Frontend routing for main dashboard
    location / {
        proxy_pass http://smm-dashboard:4002;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Tenant-ID "$tenant_id";
        proxy_set_header X-Tenant-Domain "$tenant_domain";
        proxy_set_header X-Tenant-API-URL "$tenant_api_url";
        
        # Pass tenant-specific configuration to the frontend
        sub_filter '</head>' '<script>
            window.TENANT_CONFIG = {
                tenantId: "${tenant.id}",
                domain: "${tenant.domain}",
                apiUrl: "https://${tenant.domain}/api/v1",
                theme: "${tenant.theme || 'default'}",
                logoUrl: "${tenant.logo_url || ''}",
                companyName: "${tenant.company_name || 'SMM System'}"
            };
        </script></head>';
        sub_filter_once on;
    }
    
    # Backend API routing
    location /api/ {
        proxy_pass http://smm-system:8095;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Tenant-ID "$tenant_id";
        proxy_set_header X-Tenant-Domain "$tenant_domain";
    }
    
    ${this._generateCDNBlock()}
}
`;
  }

  /**
   * Generate CDN location block
   * @returns {string} CDN block configuration
   */
  static _generateCDNBlock() {
    return `#cdn
    location /cdn/ {
        alias /etc/nginx/cdn/;

        # Cache settings
        expires 30d;
        add_header Cache-Control "public, max-age=${config.CDN_CACHE_MAX_AGE}";
        add_header X-Content-Type-Options "${config.SECURITY_HEADERS['X-Content-Type-Options']}";

        # CORS settings
        add_header Access-Control-Allow-Origin "${config.CORS_HEADERS['Access-Control-Allow-Origin']}";
        add_header Access-Control-Allow-Methods "${config.CORS_HEADERS['Access-Control-Allow-Methods']}";
        add_header Access-Control-Allow-Headers "${config.CORS_HEADERS['Access-Control-Allow-Headers']}";

        # Security headers
        add_header X-Frame-Options "${config.SECURITY_HEADERS['X-Frame-Options']}";
        add_header X-XSS-Protection "${config.SECURITY_HEADERS['X-XSS-Protection']}";

        # Enable directory listing for debugging (remove in production)
        autoindex on;

        # Limit access to image files only
        location ~* \\.(jpg|jpeg|png|gif|ico|svg|webp)$ {
            try_files $uri =404;
        }
    }`;
  }

  /**
   * Get SSL certificate path
   * @param {Object} tenant - Tenant object
   * @returns {string} SSL certificate path
   */
  static _getSSLCertPath(tenant) {
    return `${config.SSL_PATH}/${tenant.domain}.crt`;
  }

  /**
   * Get SSL key path
   * @param {Object} tenant - Tenant object
   * @returns {string} SSL key path
   */
  static _getSSLKeyPath(tenant) {
    return `${config.SSL_PATH}/${tenant.domain}.key`;
  }

  /**
   * Check if configuration content is valid
   * @param {string} configContent - Configuration content
   * @param {string} domain - Domain name
   * @returns {boolean} True if content is valid
   */
  static _hasValidContent(configContent, domain) {
    const expectedHeader = `# Tenant configuration for ${domain} - DO NOT EDIT MANUALLY`;
    return configContent.trim() !== expectedHeader;
  }

  /**
   * Reload Nginx configuration
   * @private
   */
  static async _reloadNginx() {
    try {
      logger.info('Requesting Nginx reload...');

      // Ensure signals directory exists
      FileUtils.ensureDirectoryExists(config.NGINX_SIGNALS_DIR);

      // Create signal file
      const signalFile = `${config.NGINX_SIGNALS_DIR}/reload-${Date.now()}.signal`;
      const signalData = JSON.stringify({
        action: 'reload',
        timestamp: new Date().toISOString(),
        requestedBy: 'domain-manager'
      });

      FileUtils.writeFile(signalFile, signalData, 'Nginx reload signal file');

      // Wait for reload to process
      await new Promise(resolve => setTimeout(resolve, config.NGINX_RELOAD_WAIT));

      logger.success('Nginx reload request completed');
      return true;
    } catch (err) {
      logger.error('Error requesting Nginx reload:', err.message);
      return false;
    }
  }
}

module.exports = NginxConfigService;