/**
 * SSL certificate management controller
 */
const express = require('express');
const SSLService = require('../services/ssl-service');
const { asyncHandler } = require('../middleware/error-handler');
const logger = require('../utils/logger');
const config = require('../config/constants');
const FileUtils = require('../utils/file-utils');
const router = express.Router();

/**
 * Generate SSL certificate for a specific domain
 * POST /ssl/:domain?isChildPanel=true/false
 */
router.post('/:domain', asyncHandler(async (req, res) => {
  const domain = req.params.domain;
  const isChildPanel = req.query.isChildPanel === 'true' || req.body.isChildPanel === true;
  const cleanDomain = FileUtils.cleanDomainName(domain);
  
  logger.info(`Received request to generate SSL certificate for domain: ${cleanDomain}, isChildPanel: ${isChildPanel}`);

  // Check if certificates already exist
  const nginxCertPath = `${config.SSL_PATH}/${cleanDomain}.crt`;
  const nginxKeyPath = `${config.SSL_PATH}/${cleanDomain}.key`;
  
  if (FileUtils.filesExist([nginxCertPath, nginxKeyPath])) {
    logger.info(`SSL certificate already exists for domain: ${cleanDomain}`);
    return res.json({
      success: true,
      message: `SSL certificate already exists for ${cleanDomain}`,
      isChildPanel: isChildPanel
    });
  }

  try {
    const result = await SSLService.generateCertificate(cleanDomain, isChildPanel);
    res.json({
      ...result,
      isChildPanel: isChildPanel
    });
  } catch (error) {
    logger.error(`Error in SSL certificate generation endpoint:`, error);
    
    if (error.message.includes('Another instance of Certbot')) {
      return res.status(429).json({
        error: 'Another instance of Certbot is running. Please try again later.',
        certbot_error: error.message,
        isChildPanel: isChildPanel
      });
    }

    // Return detailed error message from certbot
    const response = {
      error: `Error generating SSL certificate: ${error.message}`,
      certbot_error: error.message,
      isChildPanel: isChildPanel
    };

    // Add certbot output if available
    if (error.certbotOutput) {
      response.certbot_output = error.certbotOutput;
    }

    res.status(500).json(response);
  }
}));

/**
 * Generate wildcard SSL certificate instructions
 * POST /ssl/wildcard
 */
router.post('/wildcard', asyncHandler(async (req, res) => {
  const baseDomain = config.BASE_DOMAIN;
  const wildcardDomain = `*.${baseDomain}`;
  
  logger.info(`Received request to generate wildcard SSL certificate for domain: ${wildcardDomain}`);

  // Check if certificates already exist
  const wildcardCertPath = `${config.SSL_PATH}/${wildcardDomain}.crt`;
  const wildcardKeyPath = `${config.SSL_PATH}/${wildcardDomain}.key`;
  
  if (FileUtils.filesExist([wildcardCertPath, wildcardKeyPath])) {
    logger.info(`Wildcard SSL certificate already exists for domain: ${wildcardDomain}`);
    return res.json({
      success: true,
      message: `Wildcard SSL certificate already exists for ${wildcardDomain}`
    });
  }

  // Provide manual instructions for wildcard certificate generation
  res.status(200).json({
    message: `Wildcard SSL certificate generation requires DNS challenge authentication`,
    instructions: `To generate a wildcard certificate for ${wildcardDomain}, run the following command on your server:

certbot certonly --manual --preferred-challenges=dns --email ${config.ADMIN_EMAIL} --server https://acme-v02.api.letsencrypt.org/directory --agree-tos -d ${baseDomain} -d ${wildcardDomain}

Follow the instructions to create a TXT record for DNS verification. After successful generation, copy the certificates to Nginx:

cp /etc/letsencrypt/live/${baseDomain}/fullchain.pem ${config.SSL_PATH}/${wildcardDomain}.crt
cp /etc/letsencrypt/live/${baseDomain}/privkey.pem ${config.SSL_PATH}/${wildcardDomain}.key

Then call the /domains/refresh endpoint to update Nginx configuration.`,
    note: "Wildcard certificates cannot be generated automatically through HTTP challenge and require DNS verification."
  });
}));

module.exports = router;