/**
 * Domain management service
 */

const axios = require('axios');
const config = require('../config/constants');
const logger = require('../utils/logger');
const FileUtils = require('../utils/file-utils');
const NginxConfigService = require('./nginx-config-service');
const SSLService = require('./ssl-service');
const dnsManager = require('../dns-manager');

class DomainService {
  /**
   * Add a domain by domain name
   * @param {string} domainName - Domain name to add
   * @param {boolean} isChildPanel - Whether this is for a child panel domain
   * @returns {Promise<Object>} Result of the operation
   */
  static async addDomain(domainName, isChildPanel = false) {
    try {
      logger.info(`Getting domain information for domain: ${domainName}, isChildPanel: ${isChildPanel}`);

      // Get domain information from database
      const domainResponse = await axios.get(`${config.DB_API}/${domainName}`);

      if (!domainResponse.data || !domainResponse.data.data) {
        throw new Error(`No domain found with name: ${domainName}`);
      }

      const domainData = domainResponse.data.data;

      // Generate Nginx configuration (HTTP only initially)
      if (isChildPanel) {
        await NginxConfigService.generateChildPanelConfig(domainData, false);
      } else {
        await NginxConfigService.generateConfig(domainData, false);
      }

      // Add domain to CoreDNS
      try {
        const dnsResult = await dnsManager.addDomain(domainData.domain, config.SERVER_IP);
        logger.info(`CoreDNS domain addition result:`, dnsResult);
      } catch (dnsError) {
        logger.error(`Error adding domain to CoreDNS: ${dnsError.message}`);
        // Continue even if DNS update fails
      }

      const configType = isChildPanel ? 'Child Panel HTTP' : 'HTTP';
      
      return {
        success: true,
        message: 'Domain added successfully',
        note: `${configType} configuration generated. Generate SSL certificates to enable HTTPS. Domain added to DNS nameservers.`,
        domain: domainData,
        isChildPanel: isChildPanel
      };
    } catch (error) {
      logger.error(`Error adding domain by name: ${error.message}`);
      throw error;
    }
  }

  /**
   * Remove a domain
   * @param {string} domainName - Domain name to remove
   * @returns {Promise<Object>} Result of the operation
   */
  static async removeDomain(domainName) {
    try {
      logger.info(`Removing domain: ${domainName}`);

      // Delete domain from database
      //await axios.delete(`${config.DB_API}/${domainName}`);

      // Delete SSL certificates
      try {
        await SSLService.deleteCertificates(domainName);
        logger.success(`SSL certificates for domain ${domainName} deleted successfully`);
      } catch (certError) {
        logger.error(`Error deleting SSL certificates for domain ${domainName}: ${certError.message}`);
        // Continue with domain removal even if certificate deletion fails
      }

      // Remove Nginx configuration file
      this._removeNginxConfig(domainName);

      // Reload Nginx
      await this._reloadNginx();

      // Remove domain from CoreDNS
      try {
        const dnsResult = await dnsManager.removeDomain(domainName);
        logger.info(`CoreDNS domain removal result:`, dnsResult);
      } catch (dnsError) {
        logger.error(`Error removing domain from CoreDNS: ${dnsError.message}`);
        // Continue even if DNS update fails
      }

      return {
        success: true,
        message: 'Domain removed successfully',
        details: 'Domain configuration, SSL certificates, and DNS records have been deleted'
      };
    } catch (error) {
      logger.error(`Error removing domain: ${error.message}`);
      throw error;
    }
  }

  /**
   * Refresh Nginx configuration for all domains
   * @param {boolean} isChildPanel - Whether to use child panel configuration for all domains
   * @returns {Promise<Object>} Result of the operation
   */
  static async refreshAllConfigurations(isChildPanel = false) {
    try {
      logger.info(`Refreshing Nginx configuration for all domains, isChildPanel: ${isChildPanel}`);

      // Get all domains from the API
      const response = await axios.get(config.DB_API);
      let tenants = response.data;

      // Handle different response formats
      if (response.data && response.data.data && Array.isArray(response.data.data)) {
        tenants = response.data.data;
      } else if (!Array.isArray(tenants)) {
        logger.error('Tenant data is not an array:', tenants);
        tenants = [];
      }

      logger.info(`Found ${tenants.length} tenants for refresh`);

      // Process each tenant
      for (const tenant of tenants) {
        try {
          // Generate HTTP configuration
          if (isChildPanel) {
            await NginxConfigService.generateChildPanelConfig(tenant, false);
          } else {
            await NginxConfigService.generateConfig(tenant, false);
          }

          // Check if SSL certificates exist and generate HTTPS configuration
          const sslCertExists = this._checkSSLCertificates(tenant.domain);
          if (sslCertExists) {
            if (isChildPanel) {
              await NginxConfigService.generateChildPanelConfig(tenant, true);
            } else {
              await NginxConfigService.generateConfig(tenant, true);
            }
          }
        } catch (tenantError) {
          logger.error(`Error processing tenant ${tenant.domain}: ${tenantError.message}`);
          // Continue with other tenants
        }
      }

      // Reload Nginx to apply changes
      await this._reloadNginx();

      const configType = isChildPanel ? 'Child Panel' : 'Standard';

      return {
        success: true,
        message: 'Nginx configuration refreshed successfully',
        note: `Both HTTP and HTTPS ${configType} configurations have been updated as needed.`,
        processedTenants: tenants.length,
        isChildPanel: isChildPanel
      };
    } catch (error) {
      logger.error(`Error refreshing Nginx configuration: ${error.message}`);
      throw error;
    }
  }

  /**
   * Suspend a domain by removing its Nginx configuration
   * @param {string} domainName - Domain name to suspend
   * @returns {Promise<Object>} Result of the operation
   */
  static async suspendDomain(domainName) {
    try {
      logger.info(`Suspending domain: ${domainName}`);

      // Remove Nginx configuration file
      this._removeNginxConfig(domainName);

      // Reload Nginx to apply changes
      await this._reloadNginx();

      return {
        success: true,
        message: `Domain ${domainName} suspended successfully`,
        details: 'Nginx configuration has been removed and Nginx has been reloaded'
      };
    } catch (error) {
      logger.error(`Error suspending domain: ${error.message}`);
      throw error;
    }
  }

  /**
   * Enable a domain by regenerating its Nginx configuration
   * @param {string} domainName - Domain name to enable
   * @param {boolean} isChildPanel - Whether this is for a child panel domain
   * @returns {Promise<Object>} Result of the operation
   */
  static async enableDomain(domainName, isChildPanel = false) {
    try {
      logger.info(`Enabling domain: ${domainName}, isChildPanel: ${isChildPanel}`);

      // Get domain information from database
      const domainResponse = await axios.get(`${config.DB_API}/${domainName}`);

      if (!domainResponse.data || !domainResponse.data.data) {
        throw new Error(`No domain found with name: ${domainName}`);
      }

      const domainData = domainResponse.data.data;

      // Generate HTTP configuration
      if (isChildPanel) {
        await NginxConfigService.generateChildPanelConfig(domainData, false);
      } else {
        await NginxConfigService.generateConfig(domainData, false);
      }

      // Check if SSL certificates exist and generate HTTPS configuration
      const sslCertExists = this._checkSSLCertificates(domainData.domain);
      if (sslCertExists) {
        if (isChildPanel) {
          await NginxConfigService.generateChildPanelConfig(domainData, true);
        } else {
          await NginxConfigService.generateConfig(domainData, true);
        }
      }

      // Reload Nginx to apply changes
      await this._reloadNginx();

      const configType = isChildPanel ? 'Child Panel' : 'Standard';
      const httpsStatus = sslCertExists ? `HTTPS ${configType} configuration enabled` : `Only HTTP ${configType} configuration enabled`;

      return {
        success: true,
        message: `Domain ${domainName} enabled successfully`,
        details: `Nginx configuration has been regenerated and Nginx has been reloaded`,
        sslStatus: httpsStatus,
        isChildPanel: isChildPanel
      };
    } catch (error) {
      logger.error(`Error enabling domain: ${error.message}`);
      throw error;
    }
  }

  /**
   * Remove Nginx configuration file for a domain
   * @param {string} domainName - Domain name
   * @private
   */
  static _removeNginxConfig(domainName) {
    const safeFileName = FileUtils.createSafeFileName(domainName);
    const configPath = `${config.NGINX_CONF_DIR}/tenant-${safeFileName}.conf`;

    FileUtils.deleteFile(configPath, `Nginx configuration file for domain ${domainName}`);
  }

  /**
   * Check if SSL certificates exist for a domain
   * @param {string} domain - Domain name
   * @returns {boolean} True if certificates exist
   * @private
   */
  static _checkSSLCertificates(domain) {
    const certPath = `${config.SSL_PATH}/${domain}.crt`;
    const keyPath = `${config.SSL_PATH}/${domain}.key`;
    return FileUtils.filesExist([certPath, keyPath]);
  }

  /**
   * Reload Nginx configuration
   * @private
   */
  static async _reloadNginx() {
    try {
      logger.info('Requesting Nginx reload...');

      FileUtils.ensureDirectoryExists(config.NGINX_SIGNALS_DIR);

      const signalFile = `${config.NGINX_SIGNALS_DIR}/reload-${Date.now()}.signal`;
      const signalData = JSON.stringify({
        action: 'reload',
        timestamp: new Date().toISOString(),
        requestedBy: 'domain-manager'
      });

      FileUtils.writeFile(signalFile, signalData, 'Nginx reload signal file');

      await new Promise(resolve => setTimeout(resolve, config.NGINX_RELOAD_WAIT));
      logger.success('Nginx reload request completed');
      return true;
    } catch (err) {
      logger.error('Error requesting Nginx reload:', err.message);
      return false;
    }
  }
}

module.exports = DomainService;