package tndung.vnfb.smm.service.impl;

import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import tndung.vnfb.smm.config.AuditContextHolder;
import tndung.vnfb.smm.config.TenantContext;
import tndung.vnfb.smm.dto.response.*;
import tndung.vnfb.smm.entity.GOrder;
import tndung.vnfb.smm.entity.GService;
import tndung.vnfb.smm.helper.CommonHelper;
import tndung.vnfb.smm.repository.nontenant.GUserRepository;
import tndung.vnfb.smm.repository.tenant.GSvRepository;
import tndung.vnfb.smm.repository.tenant.OrderRepository;
import tndung.vnfb.smm.repository.tenant.TicketRepository;
import tndung.vnfb.smm.repository.tenant.TransactionRepository;
import tndung.vnfb.smm.service.DashboardV2Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
public class DashboardV2Impl implements DashboardV2Service {

    private final OrderRepository orderRepository;
    private final TicketRepository ticketRepository;
    private final GUserRepository userRepository;
    private final TransactionRepository transactionRepository;
    private final GSvRepository serviceRepository;

    private static final ZoneId ZONE_ID = AuditContextHolder.getUserZone();
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    @Override
    public DashboardV2StatsRes getDashboardV2Stats() {

        String currentTenant = TenantContext.getCurrentTenant();
        // Get today's date range
        OffsetDateTime today = OffsetDateTime.now().atZoneSameInstant(ZONE_ID).toOffsetDateTime();
        LocalDate todayDate = today.toLocalDate();
        OffsetDateTime todayStart = CommonHelper.convertStartDate(todayDate, ZONE_ID);
        OffsetDateTime todayEnd = CommonHelper.convertEndDate(todayDate, ZONE_ID);

        // Overall KPI Stats
        BigDecimal totalDeposits = transactionRepository.getTotalDeposits(currentTenant);
        BigDecimal totalSpending = transactionRepository.getTotalSpending(currentTenant);
        Long totalUsers = userRepository.getTotalUsers(currentTenant);
        Long totalOrders = orderRepository.countOrdersBetweenDates(
                OffsetDateTime.now().minusYears(10), OffsetDateTime.now(), currentTenant);
        BigDecimal totalUserBalance = transactionRepository.getTotalUserBalance(currentTenant);
        Long pendingSupport = ticketRepository.countCurrentPendingTickets(currentTenant);

        // Bonus Stats
        BigDecimal totalBonus = transactionRepository.getTotalBonus(currentTenant);
        BigDecimal todayBonus = transactionRepository.getTodayBonus(todayStart, todayEnd, currentTenant);

        // Today's Stats
        BigDecimal todayDeposits = transactionRepository.sumRevenueBetweenDates(todayStart, todayEnd, currentTenant);
        BigDecimal todaySpending = transactionRepository.getTodaySpending(todayStart, todayEnd, currentTenant);
        Long todayNewUsers = userRepository.countNewUsersBetweenDates(todayStart, todayEnd, currentTenant);
        Long todayOrders = orderRepository.countOrdersBetweenDates(todayStart, todayEnd, currentTenant);
        Long todayPendingSupport = ticketRepository.countPendingTicketsBetweenDates(todayStart, todayEnd, currentTenant);

        // Handle null values
        if (totalDeposits == null) totalDeposits = BigDecimal.ZERO;
        if (totalSpending == null) totalSpending = BigDecimal.ZERO;
        if (totalUsers == null) totalUsers = 0L;
        if (totalOrders == null) totalOrders = 0L;
        if (totalUserBalance == null) totalUserBalance = BigDecimal.ZERO;
        if (pendingSupport == null) pendingSupport = 0L;
        if (todayDeposits == null) todayDeposits = BigDecimal.ZERO;
        if (todaySpending == null) todaySpending = BigDecimal.ZERO;
        if (todayNewUsers == null) todayNewUsers = 0L;
        if (todayOrders == null) todayOrders = 0L;
        if (todayPendingSupport == null) todayPendingSupport = 0L;

        return DashboardV2StatsRes.builder()
                .totalDeposits(totalDeposits)
                .totalSpending(totalSpending.abs()) // Make spending positive for display
                .totalUsers(totalUsers)
                .totalOrders(totalOrders)
                .totalUserBalance(totalUserBalance)
                .pendingSupport(pendingSupport)
                .totalBonus(totalBonus)
                .todayBonus(todayBonus)
                .todayDeposits(todayDeposits)
                .todaySpending(todaySpending.abs()) // Make spending positive for display
                .todayNewUsers(todayNewUsers)
                .todayOrders(todayOrders)
                .todayPendingSupport(todayPendingSupport)
                .period("all-time")
                .lastUpdated(OffsetDateTime.now())
                .build();
    }

    @Override
    public HistoricalStatsRes getHistoricalStats(LocalDate startDate, LocalDate endDate, String preset) {
        return getHistoricalStats(startDate, endDate, preset, null);
    }

    @Override
    public HistoricalStatsRes getHistoricalStats(LocalDate startDate, LocalDate endDate, String preset, Long serviceId) {
        OffsetDateTime startDateTime = CommonHelper.convertStartDate(startDate, ZONE_ID);
        OffsetDateTime endDateTime = CommonHelper.convertEndDate(endDate, ZONE_ID);

        // Get deposit data (not affected by service filter)
        List<HistoricalDataPoint> deposits = getDailyDepositData(startDateTime, endDateTime);

        // Get order data (filtered by service if provided)
        List<HistoricalOrderData> orders = getDailyOrderData(startDateTime, endDateTime, serviceId);

        // Get ticket data (not affected by service filter)
        List<HistoricalTicketData> tickets = getDailyTicketData(startDateTime, endDateTime);

        // Get top services data (filtered by service if provided)
        List<TopServiceData> services = getTopServicesData(startDateTime, endDateTime, serviceId);

        return HistoricalStatsRes.builder()
                .deposits(deposits)
                .orders(orders)
                .tickets(tickets)
                .services(services)
                .build();
    }

    @Override
    public ServiceDetailsRes getServiceDetails(Long serviceId, LocalDate startDate, LocalDate endDate, Pageable pageable) {
        OffsetDateTime startDateTime = startDate != null ? CommonHelper.convertStartDate(startDate, ZONE_ID) : null;
        OffsetDateTime endDateTime = endDate != null ? CommonHelper.convertEndDate(endDate, ZONE_ID) : null;
        String currentTenant = TenantContext.getCurrentTenant();
        // Get service info
        Optional<GService> serviceOpt = serviceRepository.findByIdAndIsDeletedFalse(serviceId);
        if (serviceOpt.isEmpty()) {
            throw new RuntimeException("Service not found");
        }
        GService service = serviceOpt.get();

        // Get service statistics

        BigDecimal totalRevenue = orderRepository.sumRevenueByService(serviceId, startDateTime, endDateTime, currentTenant);
        Long totalQuantity = orderRepository.sumQuantityByService(serviceId, startDateTime, endDateTime, currentTenant);
        Long totalRefunded = orderRepository.countRefundedOrdersByService(serviceId, startDateTime, endDateTime, currentTenant);
        BigDecimal refundedAmount = orderRepository.sumRefundedAmountByService(serviceId, startDateTime, endDateTime, currentTenant);

        // Get paginated orders
        Page<GOrder> orderPage = orderRepository.findOrdersByService(
                serviceId,
                startDateTime, endDateTime, currentTenant, pageable);

        List<ServiceOrderData> orderData = orderPage.getContent().stream()
                .map(this::mapToServiceOrderData)
                .collect(Collectors.toList());
        Long totalOrders = orderPage.getTotalElements();
        // Handle null values
        if (totalRevenue == null) totalRevenue = BigDecimal.ZERO;
        if (totalQuantity == null) totalQuantity = 0L;
        if (totalRefunded == null) totalRefunded = 0L;
        if (refundedAmount == null) refundedAmount = BigDecimal.ZERO;

        return ServiceDetailsRes.builder()
                .serviceId(serviceId)
                .serviceName(service.getName())
                .totalRevenue(totalRevenue)
                .totalOrders(totalOrders)
                .totalQuantity(totalQuantity)
                .totalRefunded(totalRefunded)
                .refundedAmount(refundedAmount)
                .orders(orderData)
                .totalPages(orderPage.getTotalPages())
                .currentPage(orderPage.getNumber() + 1)
                .build();
    }

    @Override
    public TicketStatsRes getTicketStats(LocalDate startDate, LocalDate endDate) {
        OffsetDateTime startDateTime = CommonHelper.convertStartDate(startDate, ZONE_ID);
        OffsetDateTime endDateTime = CommonHelper.convertEndDate(endDate, ZONE_ID);

        // Get status distribution
        List<Object[]> statusData = ticketRepository.getTicketStatusDistribution(startDateTime, endDateTime, TenantContext.getCurrentTenant());
        List<TicketStatusData> statusDistribution = statusData.stream()
                .map(row -> {
                    String status = (String) row[0];
                    Long count = ((Number) row[1]).longValue(); // Fix BigInteger casting
                    return TicketStatusData.builder()
                            .status(status)
                            .count(count)
                            .percentage(0.0) // Will calculate after getting total
                            .build();
                })
                .collect(Collectors.toList());

        // Calculate percentages
        long totalTickets = statusDistribution.stream().mapToLong(TicketStatusData::getCount).sum();
        if (totalTickets > 0) {
            statusDistribution.forEach(data -> {
                double percentage = (data.getCount() * 100.0) / totalTickets;
                data.setPercentage(Math.round(percentage * 100.0) / 100.0);
            });
        }

        // Get daily ticket data
        List<Object[]> dailyData = ticketRepository.getDailyTicketStats(startDateTime, endDateTime, TenantContext.getCurrentTenant());
        Map<String, BigDecimal> groupedData = dailyData.stream()
                .collect(Collectors.groupingBy(
                        row -> {
                            OffsetDateTime date = CommonHelper.convertToOffsetDateTimeWithTimezone(row[0], ZONE_ID.toString());
                            // Format theo định dạng m/d/yy
                            return date.format(DateTimeFormatter.ofPattern("M/d/yy"));
                        },
                        Collectors.reducing(
                                BigDecimal.ZERO,
                                row -> {
                                    Long count = ((Number) row[1]).longValue();
                                    return BigDecimal.valueOf(count != null ? count : 0L);
                                },
                                BigDecimal::add
                        )
                ));

        List<HistoricalDataPoint> dailyTickets = groupedData.entrySet().stream()
                .map(entry -> HistoricalDataPoint.builder()
                        .date(entry.getKey())
                        .value(entry.getValue())
                        .build())
                .collect(Collectors.toList());

        return TicketStatsRes.builder()
                .statusDistribution(statusDistribution)
                .dailyTickets(dailyTickets)
                .build();
    }

    // Helper methods
    private List<HistoricalDataPoint> getDailyDepositData(OffsetDateTime startDateTime, OffsetDateTime endDateTime) {


        List<Object[]> depositData = transactionRepository.getDailyDepositStats(startDateTime, endDateTime, TenantContext.getCurrentTenant());
        Map<String, BigDecimal> groupedData = depositData.stream()
                .collect(Collectors.groupingBy(
                        row -> {
                            OffsetDateTime date = CommonHelper.convertToOffsetDateTimeWithTimezone(row[0], ZONE_ID.toString());
                            // Format theo định dạng m/d/yy
                            return date.format(DateTimeFormatter.ofPattern("M/d/yy"));
                        },
                        Collectors.reducing(
                                BigDecimal.ZERO,
                                row -> {
                                    BigDecimal value = (BigDecimal) row[1];
                                    return value != null ? value : BigDecimal.ZERO;
                                },
                                BigDecimal::add
                        )
                ));

        // Convert Map sang List<HistoricalDataPoint>
        return groupedData.entrySet().stream()
                .map(entry -> HistoricalDataPoint.builder()
                        .date(entry.getKey())
                        .value(entry.getValue())
                        .build())
                .collect(Collectors.toList());
    }

    private List<HistoricalOrderData> getDailyOrderData(OffsetDateTime startDateTime, OffsetDateTime endDateTime) {
        return getDailyOrderData(startDateTime, endDateTime, null);
    }

    private List<HistoricalOrderData> getDailyOrderData(OffsetDateTime startDateTime, OffsetDateTime endDateTime, Long serviceId) {
        List<Object[]> orderData;
        String currentTenant = TenantContext.getCurrentTenant();
        if (serviceId != null) {
            orderData = orderRepository.getDailyOrderStatsByService(startDateTime, endDateTime, serviceId, currentTenant);
        } else {
            orderData = orderRepository.getDailyOrderStats(startDateTime, endDateTime, currentTenant);
        }

        Map<String, HistoricalOrderData> groupedData = orderData.stream()
                .collect(Collectors.groupingBy(
                        row -> {
                            OffsetDateTime date = CommonHelper.convertToOffsetDateTimeWithTimezone(row[0], ZONE_ID.toString());
                            // Format theo định dạng m/d/yy
                            return date.format(DateTimeFormatter.ofPattern("M/d/yy"));
                        },
                        Collectors.reducing(
                                HistoricalOrderData.builder()
                                        .date("")
                                        .orderCount(0L)
                                        .orderValue(BigDecimal.ZERO)
                                        .build(),
                                row -> {
                                    Long orderCount = ((Number) row[1]).longValue();
                                    BigDecimal orderValue = (BigDecimal) row[2];
                                    return HistoricalOrderData.builder()
                                            .date("")
                                            .orderCount(orderCount != null ? orderCount : 0L)
                                            .orderValue(orderValue != null ? orderValue : BigDecimal.ZERO)
                                            .build();
                                },
                                (a, b) -> HistoricalOrderData.builder()
                                        .date("")
                                        .orderCount(a.getOrderCount() + b.getOrderCount())
                                        .orderValue(a.getOrderValue().add(b.getOrderValue()))
                                        .build()
                        )
                ));

        return groupedData.entrySet().stream()
                .map(entry -> HistoricalOrderData.builder()
                        .date(entry.getKey())
                        .orderCount(entry.getValue().getOrderCount())
                        .orderValue(entry.getValue().getOrderValue())
                        .build())
                .collect(Collectors.toList());
    }

    private List<HistoricalTicketData> getDailyTicketData(OffsetDateTime startDateTime, OffsetDateTime endDateTime) {
        List<Object[]> ticketData = ticketRepository.getDailyTicketStats(startDateTime, endDateTime, TenantContext.getCurrentTenant());
        Map<String, Long> groupedData = ticketData.stream()
                .collect(Collectors.groupingBy(
                        row -> {
                            OffsetDateTime date = CommonHelper.convertToOffsetDateTimeWithTimezone(row[0], ZONE_ID.toString());
                            // Format theo định dạng m/d/yy
                            return date.format(DateTimeFormatter.ofPattern("M/d/yy"));
                        },
                        Collectors.reducing(
                                0L,
                                row -> {
                                    Long count = ((Number) row[1]).longValue();
                                    return count != null ? count : 0L;
                                },
                                Long::sum
                        )
                ));

        return groupedData.entrySet().stream()
                .map(entry -> HistoricalTicketData.builder()
                        .date(entry.getKey())
                        .newTickets(entry.getValue())
                        .resolvedTickets(0L) // Would need additional query for resolved tickets
                        .pendingTickets(0L)  // Would need additional query for pending tickets
                        .build())
                .collect(Collectors.toList());
    }

    private List<TopServiceData> getTopServicesData(OffsetDateTime startDateTime, OffsetDateTime endDateTime) {
        return getTopServicesData(startDateTime, endDateTime, null);
    }

    private List<TopServiceData> getTopServicesData(OffsetDateTime startDateTime, OffsetDateTime endDateTime, Long serviceId) {
        List<Object[]> serviceData;
        String currentTenant = TenantContext.getCurrentTenant();
        if (serviceId != null) {
            serviceData = orderRepository.getServiceStatsByServiceId(startDateTime, endDateTime, serviceId, currentTenant);
        } else {
            serviceData = orderRepository.getTopServiceStats(startDateTime, endDateTime, currentTenant);
        }

        return serviceData.stream()
                .map(row -> {
                    Long svcId = ((Number) row[0]).longValue(); // Fix BigInteger casting
                    String serviceName = (String) row[1];
                    Long totalOrders = ((Number) row[2]).longValue(); // Fix BigInteger casting
                    BigDecimal totalRevenue = (BigDecimal) row[3];
                    String category = (String) row[4];
                    String platform = (String) row[5];
                    BigDecimal servicePrice = (BigDecimal) row[6];

                    return TopServiceData.builder()
                            .serviceId(svcId)
                            .serviceName(serviceName)
                            .totalOrders(totalOrders)
                            .totalRevenue(totalRevenue != null ? totalRevenue : BigDecimal.ZERO)
                            .category(category != null ? category : "Unknown")
                            .platform(platform != null ? platform : "Unknown")
                            .servicePrice(servicePrice != null ? servicePrice : BigDecimal.ZERO)
                            .build();
                })
                .collect(Collectors.toList());
    }

    @Override
    public RevenueStatsRes getRevenueStats(Integer year, Integer month) {
        // Default to current year/month if not provided
        if (year == null) year = LocalDate.now().getYear();
        if (month == null) month = LocalDate.now().getMonthValue();

        // Get month range
        LocalDate startOfMonth = LocalDate.of(year, month, 1);
        LocalDate endOfMonth = startOfMonth.withDayOfMonth(startOfMonth.lengthOfMonth());

        OffsetDateTime startDateTime = CommonHelper.convertStartDate(startOfMonth, ZONE_ID);
        OffsetDateTime endDateTime = CommonHelper.convertEndDate(endOfMonth, ZONE_ID);

        // Get daily revenue data for the month
        List<DailyRevenueData> dailyData = getDailyRevenueForMonth(startDateTime, endDateTime);

        // Get monthly data for the year
        List<MonthlyRevenueData> monthlyData = getMonthlyRevenueForYear(year);

        // Calculate summary stats
        BigDecimal totalRevenue = dailyData.stream()
                .map(DailyRevenueData::getRevenue)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal averageDaily = totalRevenue.divide(
                BigDecimal.valueOf(dailyData.size()), 2, BigDecimal.ROUND_HALF_UP);

        BigDecimal highestDay = dailyData.stream()
                .map(DailyRevenueData::getRevenue)
                .max(BigDecimal::compareTo)
                .orElse(BigDecimal.ZERO);

        return RevenueStatsRes.builder()
                .totalRevenue(totalRevenue)
                .averageDaily(averageDaily)
                .highestDay(highestDay)
                .dailyData(dailyData)
                .monthlyData(monthlyData)
                .build();
    }


    private List<DailyRevenueData> getDailyRevenueForMonth(OffsetDateTime startDateTime, OffsetDateTime endDateTime) {
        // Use transaction-based revenue data for overall stats
        List<Object[]> revenueData = transactionRepository.getDailyDepositStats(startDateTime, endDateTime, TenantContext.getCurrentTenant());

        // Create a map for existing data with timezone conversion
        Map<Integer, BigDecimal> revenueMap = revenueData.stream()
                .collect(Collectors.groupingBy(
                        row -> {
                            OffsetDateTime date = CommonHelper.convertToOffsetDateTimeWithTimezone(row[0], ZONE_ID.toString());
                            return date.getDayOfMonth();
                        },
                        Collectors.reducing(
                                BigDecimal.ZERO,
                                row -> {
                                    BigDecimal value = (BigDecimal) row[1];
                                    return value != null ? value : BigDecimal.ZERO;
                                },
                                BigDecimal::add
                        )
                ));

        // Fill all days of the month
        List<DailyRevenueData> dailyData = new ArrayList<>();
        LocalDate start = startDateTime.toLocalDate();
        int daysInMonth = start.lengthOfMonth();

        for (int day = 1; day <= daysInMonth; day++) {
            BigDecimal revenue = revenueMap.getOrDefault(day, BigDecimal.ZERO);
            String formatted = formatCurrency(revenue);

            dailyData.add(DailyRevenueData.builder()
                    .day(day)
                    .revenue(revenue)
                    .formattedRevenue(formatted)
                    .build());
        }

        return dailyData;
    }

    private List<MonthlyRevenueData> getMonthlyRevenueForYear(Integer year) {
        List<MonthlyRevenueData> monthlyData = new ArrayList<>();
        String[] monthNames = {"Tháng 1", "Tháng 2", "Tháng 3", "Tháng 4", "Tháng 5", "Tháng 6",
                "Tháng 7", "Tháng 8", "Tháng 9", "Tháng 10", "Tháng 11", "Tháng 12"};

        for (int month = 1; month <= 12; month++) {
            LocalDate startOfMonth = LocalDate.of(year, month, 1);
            LocalDate endOfMonth = startOfMonth.withDayOfMonth(startOfMonth.lengthOfMonth());

            OffsetDateTime startDateTime = CommonHelper.convertStartDate(startOfMonth, ZONE_ID);
            OffsetDateTime endDateTime = CommonHelper.convertEndDate(endOfMonth, ZONE_ID);

            // Get daily data for this month
            List<DailyRevenueData> dailyRevenues = getDailyRevenueForMonth(startDateTime, endDateTime);

            // Calculate total for the month
            BigDecimal totalRevenue = dailyRevenues.stream()
                    .map(DailyRevenueData::getRevenue)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // Convert to list of 31 values (pad with zeros)
            List<BigDecimal> dailyValues = new ArrayList<>();
            for (int day = 1; day <= 31; day++) {
                if (day <= dailyRevenues.size()) {
                    dailyValues.add(dailyRevenues.get(day - 1).getRevenue());
                } else {
                    dailyValues.add(BigDecimal.ZERO);
                }
            }

            monthlyData.add(MonthlyRevenueData.builder()
                    .month(month)
                    .monthName(monthNames[month - 1])
                    .totalRevenue(totalRevenue)
                    .dailyRevenues(dailyValues)
                    .build());
        }

        return monthlyData;
    }

    private String formatCurrency(BigDecimal amount) {
        if (amount.compareTo(BigDecimal.valueOf(1000000)) >= 0) {
            return String.format("%.1fM", amount.divide(BigDecimal.valueOf(1000000), 1, BigDecimal.ROUND_HALF_UP).doubleValue());
        } else if (amount.compareTo(BigDecimal.valueOf(1000)) >= 0) {
            return String.format("%.1fK", amount.divide(BigDecimal.valueOf(1000), 1, BigDecimal.ROUND_HALF_UP).doubleValue());
        } else {
            return amount.toString();
        }
    }

    private ServiceOrderData mapToServiceOrderData(GOrder order) {
        return ServiceOrderData.builder()
                .orderId(order.getId().toString())
                .username(order.getUser() != null ? order.getUser().getUserName() : "Unknown")
                .orderDate(order.getCreatedAt())
                .link(order.getLink())
                .quantity(order.getQuantity() != null ? order.getQuantity().longValue() : 0L)
                .price(order.getCharge() != null ? order.getCharge() : BigDecimal.ZERO)
                .status(order.getStatus() != null ? order.getStatus().toString() : "Unknown")
                .build();
    }

    @Override
    public List<TopServiceData> getAllActiveServices() {
        List<GService> services = serviceRepository.findAllByIsDeletedFalseOrderBySortAsc();
        return services.stream()
                .map(service -> TopServiceData.builder()
                        .serviceId(service.getId())
                        .serviceName(service.getName())
                        .servicePrice(service.getPrice() != null ? service.getPrice() : BigDecimal.ZERO)
                        .totalOrders(0L) // Default value for dropdown display
                        .totalRevenue(service.getPrice() != null ? service.getPrice() : BigDecimal.ZERO)
                        .category(service.getCategory() != null ? service.getCategory().getName() : "Unknown")
                        .platform(service.getCategory() != null && service.getCategory().getPlatform() != null
                                ? service.getCategory().getPlatform().getName() : "Unknown")
                        .build())
                .collect(Collectors.toList());
    }
}
