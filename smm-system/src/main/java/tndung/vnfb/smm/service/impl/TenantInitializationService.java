package tndung.vnfb.smm.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.FileCopyUtils;
import tndung.vnfb.smm.entity.Tenant;

import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;

@Service
@RequiredArgsConstructor
@Slf4j
public class TenantInitializationService {

    private final JdbcTemplate jdbcTemplate;

    /**
     * Initialize default data for a new tenant using SQL script template
     *
     * @param tenant The tenant to initialize
     */
    @Transactional
    public void initializeTenantDefaultData(Tenant tenant) {
        try {
            log.info("Initializing default data for tenant: {}", tenant.getId());

            // Read SQL script template from resources
            String sqlScript = readSqlTemplate();

            // Replace placeholder with actual tenant ID
            String processedScript = sqlScript.replace("{tenantId}", tenant.getId());

            // Split script by semicolons and execute each statement
            String[] statements = processedScript.split(";");

            for (String statement : statements) {
                String trimmedStatement = statement.trim();
                if (!trimmedStatement.isEmpty() && !trimmedStatement.startsWith("--")) {
                    try {
                        jdbcTemplate.execute(trimmedStatement);
                        log.debug("Executed SQL statement for tenant {}: {}", tenant.getId(),
                                trimmedStatement.substring(0, Math.min(50, trimmedStatement.length())) + "...");
                    } catch (Exception e) {
                        log.error("Error executing SQL statement for tenant {}: {}",
                                tenant.getId(), e.getMessage());
                        throw e; // Re-throw to trigger rollback
                    }
                }
            }

            log.info("Successfully initialized default data for tenant: {}", tenant.getId());

        } catch (Exception e) {
            log.error("Failed to initialize default data for tenant {}: {}",
                    tenant.getId(), e.getMessage(), e);
            throw new RuntimeException("Failed to initialize tenant default data", e);
        }
    }

    /**
     * Read SQL template file from resources
     *
     * @return The content of the SQL template
     */
    private String readSqlTemplate() throws IOException {
        ClassPathResource resource = new ClassPathResource("sql/tenant-default-data.sql");

        if (!resource.exists()) {
            throw new IOException("SQL template file not found: " + "sql/tenant-default-data.sql");
        }

        try (InputStreamReader reader = new InputStreamReader(resource.getInputStream(), StandardCharsets.UTF_8)) {
            return FileCopyUtils.copyToString(reader);
        }
    }

    /**
     * Initialize tenant with custom SQL script content
     *
     * @param tenant The tenant to initialize
     * @param customScript Custom SQL script content
     */
    @Transactional
    public void initializeTenantWithCustomScript(Tenant tenant, String customScript) {
        try {
            log.info("Initializing tenant {} with custom script", tenant.getId());

            // Replace placeholder with actual tenant ID
            String processedScript = customScript.replace("{tenantId}", tenant.getId());

            // Split script by semicolons and execute each statement
            String[] statements = processedScript.split(";");

            for (String statement : statements) {
                String trimmedStatement = statement.trim();
                if (!trimmedStatement.isEmpty() && !trimmedStatement.startsWith("--")) {
                    jdbcTemplate.execute(trimmedStatement);
                }
            }

            log.info("Successfully initialized tenant {} with custom script", tenant.getId());

        } catch (Exception e) {
            log.error("Failed to initialize tenant {} with custom script: {}",
                    tenant.getId(), e.getMessage(), e);
            throw new RuntimeException("Failed to initialize tenant with custom script", e);
        }
    }
}