<div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
  <div class="max-w-md w-full bg-white rounded-2xl shadow-xl overflow-hidden">
    
    <!-- Loading State -->
    <div *ngIf="isLoading" class="text-center p-8">
      <div class="verification-icon mb-6">
        <i class="fas fa-spinner fa-spin text-5xl text-blue-500"></i>
      </div>
      <h2 class="text-2xl font-bold text-gray-800 mb-4">
        {{ 'profile.email_verification.verifying' | translate }}
      </h2>
      <p class="text-gray-600">
        {{ 'profile.email_verification.please_wait' | translate }}
      </p>
    </div>
 
    <!-- Success State -->
    <div *ngIf="isSuccess" class="text-center p-8">
      <div class="verification-icon mb-6">
        <i class="fas fa-check-circle text-5xl text-green-500"></i>
      </div>
      <h2 class="text-2xl font-bold text-gray-800 mb-4">
        {{ 'profile.email_verification.success_title' | translate }}
      </h2>
      <p class="text-gray-600 mb-6">
        {{ 'profile.email_verification.success_message' | translate }}
      </p>
      <div class="space-y-3">
        <button
          (click)="goToLogin()"
          class="w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors font-medium">
          <i class="fas fa-sign-in-alt mr-2" *ngIf="!authService.isAuthenticated()"></i>
          <i class="fas fa-user-cog mr-2" *ngIf="authService.isAuthenticated()"></i>
          {{ authService.isAuthenticated() ? ('common.go_to_dashboard' | translate) : ('profile.email_verification.go_to_login' | translate) }}
        </button>
        <button
          (click)="goToHome()"
          class="w-full bg-gray-200 text-gray-700 py-3 px-6 rounded-lg hover:bg-gray-300 transition-colors font-medium">
          <i class="fas fa-home mr-2"></i>
          {{ 'profile.email_verification.go_to_home' | translate }}
        </button>
      </div>
    </div>

    <!-- Error State -->
    <div *ngIf="!isLoading && hasError" class="text-center p-8">
      <div class="verification-icon mb-6">
        <i class="fas fa-exclamation-triangle text-5xl text-red-500"></i>
      </div>
      <h2 class="text-2xl font-bold text-gray-800 mb-4">
        {{ 'profile.email_verification.error_title' | translate }}
      </h2>
      <p class="text-red-600 mb-6">
        {{ errorMessage }}
      </p>
      <div class="space-y-3">
        <button
          (click)="goToLogin()"
          class="w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors font-medium">
          <i class="fas fa-sign-in-alt mr-2" *ngIf="!authService.isAuthenticated()"></i>
          <i class="fas fa-user-cog mr-2" *ngIf="authService.isAuthenticated()"></i>
          {{ 'profile.email_verification.go_to_login' | translate }}
        </button>
        <!-- <button
          (click)="goToHome()"
          class="w-full bg-gray-200 text-gray-700 py-3 px-6 rounded-lg hover:bg-gray-300 transition-colors font-medium">
          <i class="fas fa-home mr-2"></i>
          {{ 'profile.email_verification.go_to_home' | translate }}
        </button> -->
      </div>
    </div>
    
  </div>
</div>