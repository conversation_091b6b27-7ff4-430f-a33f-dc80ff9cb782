const express = require('express');
const config = require('./config/constants');
const logger = require('./utils/logger');
const errorHandler = require('./middleware/error-handler');

// Controllers
const domainController = require('./controllers/domain-controller');
const sslController = require('./controllers/ssl-controller');
const dnsController = require('./controllers/dns-controller');
const logoGeneratorController = require('./controllers/logo-generator-controller');
const { router: telegramBotController, initializeTelegramBot } = require('./controllers/telegram-bot-controller');

logger.info('Domain manager starting in container-friendly mode');

const app = express();

// Middleware
app.use(express.json());

// Routes
app.use('/domains', domainController);
app.use('/ssl', sslController);
app.use('/dns', dnsController);
// app.use('/subdomains', require('./controllers/subdomain-controller'));
app.use('/logo-generator', logoGeneratorController);
app.use('/telegram', telegramBotController);

// 404 handler
app.use(errorHandler.notFoundHandler);

// Error handler
app.use(errorHandler.errorHandler);

// Initialize Telegram Bot
initializeTelegramBot().then((initialized) => {
  if (initialized) {
    logger.info('Telegram bot initialized successfully');
  } else {
    logger.warn('Telegram bot initialization failed - continuing without bot');
  }
}).catch((error) => {
  logger.error('Error during Telegram bot initialization:', error);
});

// Start server
app.listen(config.PORT, () => {
  logger.info(`Domain manager service running on port ${config.PORT}`);
});