import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { BehaviorSubject, Observable, EMPTY, throwError } from 'rxjs';
import { catchError, finalize } from 'rxjs/operators';

// Services
import { AuthService } from '../../../core/services/auth.service';
import { ToastService } from '../../../core/services/toast.service';
import { MfaStateService } from '../../../core/services/mfa-state.service';
import { NotifyType } from '../../../constant/notify-type';

export interface AuthState {
  loginForm: FormGroup;
  returnUrl: string;
  isLoading: boolean;
  loginError: string;
  activeTab: number;
}

@Injectable({
  providedIn: 'root'
})
export class AuthLogicService {
  private readonly DEFAULT_REDIRECT_URL = '/dashboard/new';
  private readonly REDIRECT_STORAGE_KEY = 'redirectAfterLogin';
  private readonly EMAIL_STORAGE_KEY = 'userEmail';

  private stateSubject = new BehaviorSubject<AuthState>({
    loginForm: new FormGroup({
      email: new FormControl('', [Validators.required]),
      password: new FormControl('', [Validators.required]),
      rememberMe: new FormControl(false)
    }),
    returnUrl: this.DEFAULT_REDIRECT_URL,
    isLoading: false,
    loginError: '',
    activeTab: 1
  });

  public state$ = this.stateSubject.asObservable();

  constructor(
    private authService: AuthService,
    private route: ActivatedRoute,
    private toastService: ToastService,
    private mfaStateService: MfaStateService,
    private router: Router
   
  ) {
    this.initializeAuth();
  }

  private initializeAuth(): void {
    this.initFormWithSavedData();
    this.setupRouteParams();
    this.checkExistingAuth();
  }

  private setupRouteParams(): void {
    this.route.queryParams.subscribe(params => {
      const currentState = this.stateSubject.value;
      const returnUrl = this.safeDecodeUrl(params['returnUrl']) || this.DEFAULT_REDIRECT_URL;
      console.log('Return URL from query params:', returnUrl);
      
      this.updateState({ returnUrl });
    });
  }

  private checkExistingAuth(): void {
    if (this.authService.isAuthenticated() && !this.authService.isTokenExpired()) {
      console.log('Auth Component - User is already authenticated, redirecting');
      this.navigateToStoredOrDefaultUrl();
    }
  }

  private initFormWithSavedData(): void {
 
      const savedEmail = localStorage.getItem(this.EMAIL_STORAGE_KEY);
      if (savedEmail) {
        const currentState = this.stateSubject.value;
        currentState.loginForm.patchValue({
          email: savedEmail,
          rememberMe: true
        });
        this.stateSubject.next(currentState);
      }
  
  }

  /**
   * Safely decode URL with fallback to original value if decode fails
   */
  private safeDecodeUrl(url: string | null | undefined): string | null {
    if (!url) return null;
    
    try {
      const decoded = decodeURIComponent(url);
      console.log('URL decoded successfully:', { original: url, decoded });
      return decoded;
    } catch (error) {
      console.warn('Failed to decode URL, using original:', { url, error });
      return url;
    }
  }

  /**
   * Get redirect URL from localStorage or fallback to default
   */
  private getRedirectUrl(): string {
  
      return this.DEFAULT_REDIRECT_URL;
    

    const storedUrl = localStorage.getItem(this.REDIRECT_STORAGE_KEY);
    return this.safeDecodeUrl(storedUrl) || this.DEFAULT_REDIRECT_URL;
  }

  /**
   * Navigate to stored redirect URL or default URL
   */
  private navigateToStoredOrDefaultUrl(): void {
    const redirectUrl = this.getRedirectUrl();
    
   
      localStorage.removeItem(this.REDIRECT_STORAGE_KEY);
    
    
    console.log('Navigating to URL:', redirectUrl);
    this.navigateToUrl(redirectUrl);
  }

  /**
   * Navigate to URL using window.location.href
   */
  private navigateToUrl(url: string): void {
    try {
      console.log('Navigating to URL:', url);
      
      // Ensure URL starts with / for relative paths
      if (!url.startsWith('/') && !url.startsWith('http')) {
        url = '/' + url;
      }
      
      window.location.href = url;
    } catch (error) {
      console.error('Error navigating to URL:', error);
      // Fallback to default URL
      window.location.href = this.DEFAULT_REDIRECT_URL;
    }
  }

  /**
   * Handle successful login navigation
   */
  private handleSuccessfulLogin(): void {
    console.log('Login successful');
    this.toastService.showToast('Login successful!', NotifyType.SUCCESS);

    // Check stored redirect URL first
  
      const storedUrl = localStorage.getItem(this.REDIRECT_STORAGE_KEY);
      if (storedUrl) {
        const decodedUrl = this.safeDecodeUrl(storedUrl) || this.DEFAULT_REDIRECT_URL;
        console.log('Navigating to stored URL:', decodedUrl);
        localStorage.removeItem(this.REDIRECT_STORAGE_KEY);
        this.navigateToUrl(decodedUrl);
        return;
      }
    
    
    // Fallback to returnUrl from state
    const currentState = this.stateSubject.value;
    const returnUrl = this.safeDecodeUrl(currentState.returnUrl) || this.DEFAULT_REDIRECT_URL;
    console.log('Navigating to return URL:', returnUrl);
    this.navigateToUrl(returnUrl);
  }

  /**
   * Handle MFA required error
   */
  private handleMfaRequired(error: any, email: string, password: string): void {
    const currentState = this.stateSubject.value;
    
    this.mfaStateService.storeMfaInfo(
      email,
      password,
      error.data.login_first_factor,
      currentState.returnUrl || this.DEFAULT_REDIRECT_URL
    );

    this.router.navigate(['/auth/mfa']);
  }

  public onSubmit(): void {
    const currentState = this.stateSubject.value;
    
    if (currentState.loginForm.status !== 'VALID') {
      console.log('Login form is invalid');
      this.markFormControlsAsTouched();
      return;
    }

    console.log('Login form submitted');
    this.updateState({ isLoading: true, loginError: '' });

    const email = currentState.loginForm.controls['email'].value;
    const password = currentState.loginForm.controls['password'].value;

    this.authService
      .login(email, password)
      .pipe(
        catchError(err => {
          console.error('Login error:', err);

          // Check if this is an MFA required error
          if (this.isMfaRequiredError(err)) {
            this.handleMfaRequired(err, email, password);
            return EMPTY;
          }

          // Handle other errors
          const loginError = err.message || 'Login failed. Please check your credentials and try again.';
          this.updateState({ loginError });
          this.toastService.showToast(loginError, NotifyType.ERROR);
          return throwError(() => err);
        }),
        finalize(() => {
          this.updateState({ isLoading: false });
        })
      )
      .subscribe({
        next: () => this.handleSuccessfulLogin(),
        error: (error) => {
          console.error('Login subscription error:', error);
        }
      });
  }

  /**
   * Check if error is MFA required
   */
  private isMfaRequiredError(error: any): boolean {
    return error && 
           error.code === 400 &&
           error.message === 'Please enter the TOTP code to continue logging in' &&
           error.data && 
           error.data.login_first_factor;
  }

  /**
   * Mark all form controls as touched to show validation errors
   */
  private markFormControlsAsTouched(): void {
    const currentState = this.stateSubject.value;
    Object.keys(currentState.loginForm.controls).forEach(key => {
      currentState.loginForm.controls[key].markAllAsTouched();
    });
  }

  public loginWithGoogle(): void {
    console.log('Login with Google clicked');
    // TODO: Implement Google authentication
  }

  public setActiveTab(tab: number): void {
    this.updateState({ activeTab: tab });
  }

  private updateState(partialState: Partial<AuthState>): void {
    const currentState = this.stateSubject.value;
    this.stateSubject.next({
      ...currentState,
      ...partialState
    });
  }

  // Getters for form controls
  public get email(): FormControl {
    return this.stateSubject.value.loginForm.get('email') as FormControl;
  }

  public get password(): FormControl {
    return this.stateSubject.value.loginForm.get('password') as FormControl;
  }

  public get rememberMe(): FormControl {
    return this.stateSubject.value.loginForm.get('rememberMe') as FormControl;
  }
}