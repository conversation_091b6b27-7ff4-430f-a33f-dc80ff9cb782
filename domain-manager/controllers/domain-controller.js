/**
 * Domain management controller
 */
const express = require('express');
const DomainService = require('../services/domain-service');
const { asyncHandler } = require('../middleware/error-handler');
const logger = require('../utils/logger');
const router = express.Router();

/**
 * Add a domain by domain name
 * POST /domains/:domain?isChildPanel=true/false
 */
router.post('/:domain', asyncHandler(async (req, res) => {
  const domain = req.params.domain;
  const isChildPanel = req.query.isChildPanel === 'true' || req.body.isChildPanel === true;
  
  logger.info(`Received request to add domain: ${domain}, isChildPanel: ${isChildPanel}`);
  
  const result = await DomainService.addDomain(domain, isChildPanel);
  res.status(201).json({
    ...result,
    isChildPanel: isChildPanel
  });
}));

/**
 * Remove a domain
 * DELETE /domains/:name
 */
router.delete('/:name', asyncHandler(async (req, res) => {
  const domainName = req.params.name;
  logger.info(`Received request to remove domain: ${domainName}`);
  
  const result = await DomainService.removeDomain(domainName);
  res.json(result);
}));

/**
 * Refresh Nginx configuration for all domains
 * POST /domains/refresh?isChildPanel=true/false
 */
router.post('/refresh', asyncHandler(async (req, res) => {
  const isChildPanel = req.query.isChildPanel === 'true' || req.body.isChildPanel === true;
  
  logger.info(`Received request to refresh all domain configurations, isChildPanel: ${isChildPanel}`);
  
  const result = await DomainService.refreshAllConfigurations(isChildPanel);
  res.json({
    ...result,
    isChildPanel: isChildPanel
  });
}));

/**
 * Suspend a domain by removing its Nginx configuration
 * POST /domains/:domain/suspend
 */
router.post('/:domain/suspend', asyncHandler(async (req, res) => {
  const domain = req.params.domain;
  logger.info(`Received request to suspend domain: ${domain}`);
  
  const result = await DomainService.suspendDomain(domain);
  res.json(result);
}));

/**
 * Enable a domain by regenerating its Nginx configuration
 * POST /domains/:domain/enable?isChildPanel=true/false
 */
router.post('/:domain/enable', asyncHandler(async (req, res) => {
  const domain = req.params.domain;
  const isChildPanel = req.query.isChildPanel === 'true' || req.body.isChildPanel === true;
  
  logger.info(`Received request to enable domain: ${domain}, isChildPanel: ${isChildPanel}`);
  
  const result = await DomainService.enableDomain(domain, isChildPanel);
  res.json({
    ...result,
    isChildPanel: isChildPanel
  });
}));

module.exports = router;