package tndung.vnfb.smm.repository.tenant;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import tndung.vnfb.smm.constant.enums.TicketStatus;
import tndung.vnfb.smm.entity.Ticket;

import java.time.OffsetDateTime;
import java.util.List;

public interface TicketRepository extends TenantAwareRepository<Ticket, Long> {
    @Query("SELECT t FROM Ticket t WHERE t.status = :status AND t.tenantId = :#{T(tndung.vnfb.smm.config.TenantContext).getWildcardTenant()}")
    List<Ticket> findByStatus(@Param("status") TicketStatus status);
    @Query("SELECT COUNT(t) FROM Ticket t WHERE t.status = tndung.vnfb.smm.constant.enums.TicketStatus.Pending" +
            " AND t.createdAt BETWEEN :startDate AND :endDate " +
            " AND t.tenantId = :tenantId ")
    Long countPendingTicketsBetweenDates(@Param("startDate") OffsetDateTime startDate, @Param("endDate") OffsetDateTime endDate,


             @Param("tenantId") String tenantId);

    // Dashboard V2 queries
    @Query(value = "SELECT t.status as status, COUNT(t) as count " +
            "FROM ticket t WHERE t.created_at BETWEEN :startDate AND :endDate " +
            "AND t.tenant_id = :tenantId " +
            "GROUP BY t.status", nativeQuery = true)
    List<Object[]> getTicketStatusDistribution(@Param("startDate") OffsetDateTime startDate, @Param("endDate") OffsetDateTime endDate,

                                               @Param("tenantId") String tenantId);

    @Query(value = "SELECT t.created_at as date, COUNT(t) as count " +
            "FROM ticket t WHERE t.created_at BETWEEN :startDate AND :endDate " +
            "AND t.tenant_id = :tenantId " +
            "GROUP BY t.created_at ORDER BY t.created_at", nativeQuery = true)
    List<Object[]> getDailyTicketStats(@Param("startDate") OffsetDateTime startDate, @Param("endDate") OffsetDateTime endDate, @Param("tenantId") String tenantId);

    @Query(value = "SELECT COUNT(t) FROM ticket t WHERE t.status = 'Pending' " +
            "AND t.tenant_id = :tenantId", nativeQuery = true)
    Long countCurrentPendingTickets( @Param("tenantId") String tenantId);


    @Query("SELECT t FROM Ticket t WHERE " +
            "(:keyword IS NULL OR LOWER(t.description) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
            "LOWER(t.subject) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
            "CAST(t.id AS string) LIKE CONCAT('%', :keyword, '%')) AND " +
            "(:status IS NULL OR t.status = :status) AND " +
            "(:userName IS NULL OR LOWER(t.createdBy) LIKE LOWER(CONCAT('%', :userName, '%'))) AND " +
            "( CAST(:startDate AS timestamp)  IS NULL OR  t.createdAt >= :startDate) AND " +
            "( CAST(:endDate AS timestamp) IS NULL OR t.createdAt <= :endDate) " +
            " AND t.tenantId = :#{T(tndung.vnfb.smm.config.TenantContext).getWildcardTenant()} " +
            "ORDER BY t.updatedAt DESC")
    Page<Ticket> findByFilters(
            @Param("keyword") String keyword,
            @Param("status") TicketStatus status,
            @Param("userName") String userName,
            @Param("startDate") OffsetDateTime startDate,
            @Param("endDate") OffsetDateTime endDate,
            Pageable pageable
    );


    @Query("SELECT t FROM Ticket t WHERE " +
            "(:keyword IS NULL OR LOWER(t.description) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
            "LOWER(t.subject) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
            "CAST(t.id AS string) LIKE CONCAT('%', :keyword, '%')) AND " +
            "(:userName IS NULL OR LOWER(t.createdBy) LIKE LOWER(CONCAT('%', :userName, '%')))" +
            " AND t.tenantId = :#{T(tndung.vnfb.smm.config.TenantContext).getWildcardTenant()} " +
            " order by t.updatedAt desc ")
    Page<Ticket> findByMyFilters(
            @Param("keyword") String keyword,
            @Param("userName") String userName,
            Pageable pageable
    );
}
