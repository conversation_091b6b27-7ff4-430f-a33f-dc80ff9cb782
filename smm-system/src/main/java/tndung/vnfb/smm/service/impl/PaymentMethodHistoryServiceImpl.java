package tndung.vnfb.smm.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tndung.vnfb.smm.config.AuditContextHolder;
import tndung.vnfb.smm.config.TenantContext;
import tndung.vnfb.smm.entity.PaymentMethod;
import tndung.vnfb.smm.entity.PaymentMethodHistory;
import tndung.vnfb.smm.model.request.PaymentMethodHistoryFilterReq;
import tndung.vnfb.smm.model.response.PaymentMethodHistoryRes;
import tndung.vnfb.smm.model.response.PaymentMethodStatisticsRes;
import tndung.vnfb.smm.repository.tenant.PaymentMethodHistoryRepository;
import tndung.vnfb.smm.repository.tenant.TransactionRepository;
import tndung.vnfb.smm.repository.nontenant.GUserRepository;
import tndung.vnfb.smm.service.PaymentMethodHistoryService;
import tndung.vnfb.smm.dto.response.TransactionWithUserRes;
import tndung.vnfb.smm.entity.GTransaction;
import tndung.vnfb.smm.entity.GUser;
import tndung.vnfb.smm.constant.enums.TransactionType;
import tndung.vnfb.smm.helper.CommonHelper;
import org.springframework.data.domain.PageImpl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class PaymentMethodHistoryServiceImpl implements PaymentMethodHistoryService {

    private final PaymentMethodHistoryRepository paymentMethodHistoryRepository;
    private final TransactionRepository transactionRepository;
    private final GUserRepository gUserRepository;
    private final ObjectMapper objectMapper;
    private static final ZoneId ZONE_ID = AuditContextHolder.getUserZone();

    @Override
    @Transactional
    public void logPaymentMethodAction(PaymentMethod paymentMethod, PaymentMethodHistory.ActionType actionType) {
        logPaymentMethodAction(paymentMethod, actionType, null, null, null);
    }

    @Override
    @Transactional
    public void logPaymentMethodAction(PaymentMethod paymentMethod, PaymentMethodHistory.ActionType actionType,
                                       List<String> changedFields, String oldValues, String newValues) {
        try {
            PaymentMethodHistory history = PaymentMethodHistory.createFromPaymentMethod(paymentMethod, actionType);
            history.setTenantId(TenantContext.getCurrentTenant());

            if (changedFields != null && !changedFields.isEmpty()) {
                history.setChangedFields(objectMapper.writeValueAsString(changedFields));
            }
            if (oldValues != null) {
                history.setOldValues(oldValues);
            }
            if (newValues != null) {
                history.setNewValues(newValues);
            }

            paymentMethodHistoryRepository.save(history);
            log.debug("Logged payment method action: {} for payment method ID: {}", actionType, paymentMethod.getId());
        } catch (JsonProcessingException e) {
            log.error("Error serializing changed fields for payment method history", e);
            // Save without changed fields if serialization fails
            PaymentMethodHistory history = PaymentMethodHistory.createFromPaymentMethod(paymentMethod, actionType);
            history.setTenantId(TenantContext.getCurrentTenant());
            paymentMethodHistoryRepository.save(history);
        }
    }

    @Override
    public Page<PaymentMethodHistoryRes> getPaymentMethodHistory(PaymentMethodHistoryFilterReq filterReq, Pageable pageable) {
        OffsetDateTime startDateTime = CommonHelper.convertStartDate(filterReq.getStartDate(), ZONE_ID);
        OffsetDateTime endDateTime = CommonHelper.convertEndDate(filterReq.getEndDate(), ZONE_ID);


        Page<PaymentMethodHistory> historyPage = paymentMethodHistoryRepository.findWithFilters(
                filterReq.getPaymentMethodId(),
                filterReq.getActionType(),
                startDateTime,
                endDateTime,
                pageable
        );

        return historyPage.map(PaymentMethodHistoryRes::fromEntity);
    }

    @Override
    public Page<PaymentMethodHistoryRes> getPaymentMethodHistoryByPaymentMethodId(Long paymentMethodId, Pageable pageable) {
        Page<PaymentMethodHistory> historyPage = paymentMethodHistoryRepository.findByPaymentMethodId(paymentMethodId, pageable);
        return historyPage.map(PaymentMethodHistoryRes::fromEntity);
    }

    @Override
    public PaymentMethodStatisticsRes getPaymentMethodStatistics(LocalDate startDate, LocalDate endDate) {
        OffsetDateTime startDateTime = CommonHelper.convertStartDate(startDate, ZONE_ID);
        OffsetDateTime endDateTime = CommonHelper.convertEndDate(endDate, ZONE_ID);
//
//        // Get basic counts
//        Long totalRecords = paymentMethodHistoryRepository.countByDateRange(startDateTime, endDateTime);
//        Long totalCreated = paymentMethodHistoryRepository.countByActionTypeAndDateRange(PaymentMethodHistory.ActionType.CREATE, startDateTime, endDateTime);
//        Long totalUpdated = paymentMethodHistoryRepository.countByActionTypeAndDateRange(PaymentMethodHistory.ActionType.UPDATE, startDateTime, endDateTime);
//        Long totalDeleted = paymentMethodHistoryRepository.countByActionTypeAndDateRange(PaymentMethodHistory.ActionType.DELETE, startDateTime, endDateTime);
//        Long totalActivated = paymentMethodHistoryRepository.countByActionTypeAndDateRange(PaymentMethodHistory.ActionType.ACTIVATE, startDateTime, endDateTime);
//        Long totalDeactivated = paymentMethodHistoryRepository.countByActionTypeAndDateRange(PaymentMethodHistory.ActionType.DEACTIVATE, startDateTime, endDateTime);

        // Get provider statistics
        List<PaymentMethodStatisticsRes.ProviderStatistic> providerStats = getProviderStatistics(startDateTime, endDateTime);

        // Get transaction statistics
        List<PaymentMethodStatisticsRes.PaymentMethodTransactionStatistic> transactionStats = getPaymentMethodTransactionStatistics(startDate, endDate);

        // Get action type statistics
        //  List<PaymentMethodStatisticsRes.ActionTypeStatistic> actionTypeStats = getActionTypeStatistics(startDate, endDate);

        return PaymentMethodStatisticsRes.builder()
//                .totalRecords(totalRecords)
//                .totalCreated(totalCreated)
//                .totalUpdated(totalUpdated)
//                .totalDeleted(totalDeleted)
//                .totalActivated(totalActivated)
//                .totalDeactivated(totalDeactivated)
                .providerStatistics(providerStats)
                .transactionStatistics(transactionStats)
                //  .actionTypeStatistics(actionTypeStats)
                .build();
    }

    @Override
    public List<PaymentMethodHistoryRes> getRecentActivity(int limit) {
        Pageable pageable = PageRequest.of(0, limit);
        List<PaymentMethodHistory> recentHistory = paymentMethodHistoryRepository.findRecentActivity(pageable);
        return recentHistory.stream()
                .map(PaymentMethodHistoryRes::fromEntity)
                .collect(Collectors.toList());
    }

    @Override
    public List<PaymentMethodStatisticsRes.ProviderStatistic> getProviderStatistics(OffsetDateTime startDate, OffsetDateTime endDate) {
        List<Object[]> results = paymentMethodHistoryRepository.getProviderStatistics(startDate, endDate);

        // Calculate total amount for percentage calculation
        BigDecimal totalAmount = results.stream()
                .map(row -> (BigDecimal) row[3]) // total_amount
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        return results.stream()
                .map(row -> {
                    String providerId = (String) row[0];
                    String providerName = (String) row[1];
                    Long methodCount = ((Number) row[2]).longValue(); // method_count (for reference)
                    BigDecimal providerTotalAmount = (BigDecimal) row[3]; // total_amount
                    Long totalTransactions = ((Number) row[4]).longValue();

                    // Calculate percentage based on total amount instead of method count
                    BigDecimal percentage = totalAmount.compareTo(BigDecimal.ZERO) > 0
                            ? providerTotalAmount.multiply(BigDecimal.valueOf(100))
                            .divide(totalAmount, 2, RoundingMode.HALF_UP)
                            : BigDecimal.ZERO;

                    return PaymentMethodStatisticsRes.ProviderStatistic.builder()
                            .providerId(providerId)
                            .providerName(providerName)
                            .count(providerTotalAmount) // Use total amount as count for display
                            .percentage(percentage)
                            .build();
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<PaymentMethodStatisticsRes.ActionTypeStatistic> getActionTypeStatistics(LocalDate startDate, LocalDate endDate) {
        OffsetDateTime startDateTime = CommonHelper.convertStartDate(startDate, ZONE_ID);
        OffsetDateTime endDateTime = CommonHelper.convertEndDate(endDate, ZONE_ID);

        List<Object[]> results = paymentMethodHistoryRepository.getActionTypeStatistics(startDateTime, endDateTime);
        long totalCount = results.stream().mapToLong(r -> (Long) r[1]).sum();

        return results.stream()
                .map(result -> {
                    PaymentMethodHistory.ActionType actionType = (PaymentMethodHistory.ActionType) result[0];
                    Long count = (Long) result[1];
                    BigDecimal percentage = totalCount > 0
                            ? BigDecimal.valueOf(count * 100.0 / totalCount).setScale(2, RoundingMode.HALF_UP)
                            : BigDecimal.ZERO;

                    return PaymentMethodStatisticsRes.ActionTypeStatistic.builder()
                            .actionType(actionType.name())
                            .actionTypeDisplay(getActionTypeDisplay(actionType))
                            .count(count)
                            .percentage(percentage)
                            .build();
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<PaymentMethodStatisticsRes.PaymentMethodTransactionStatistic> getPaymentMethodTransactionStatistics(LocalDate startDate, LocalDate endDate) {
        OffsetDateTime startDateTime = CommonHelper.convertStartDate(startDate, ZONE_ID);
        OffsetDateTime endDateTime = CommonHelper.convertEndDate(endDate, ZONE_ID);
        List<Object[]> results = paymentMethodHistoryRepository.getPaymentMethodTransactionStatistics(startDateTime, endDateTime);

        return results.stream()
                .map(result -> {
                    Long paymentMethodId = ((Number) result[0]).longValue();
                    String paymentMethodName = (String) result[1];
                    String providerId = (String) result[2];
                    String providerName = (String) result[3];
                    String bankCode = (String) result[4];
                    String bankName = (String) result[5];
                    BigDecimal totalAmount = (BigDecimal) result[6];
                    Long totalTransactions = ((Number) result[7]).longValue();
                    BigDecimal averageAmount = (BigDecimal) result[8];
                    OffsetDateTime lastTransactionDate = result[9] != null ?
                            ((java.sql.Timestamp) result[9]).toInstant().atOffset(AuditContextHolder.getUserZone()) : null;
                    Boolean isActive = (Boolean) result[10];

                    return PaymentMethodStatisticsRes.PaymentMethodTransactionStatistic.builder()
                            .paymentMethodId(paymentMethodId)
                            .paymentMethodName(paymentMethodName)
                            .providerId(providerId)
                            .providerName(providerName)
                            .bankCode(bankCode)
                            .bankName(bankName)
                            .totalAmount(totalAmount)
                            .totalTransactions(totalTransactions)
                            .averageAmount(averageAmount)
                            .lastTransactionDate(lastTransactionDate)
                            .isActive(isActive)
                            .build();
                })
                .collect(Collectors.toList());
    }

    private String getActionTypeDisplay(PaymentMethodHistory.ActionType actionType) {
        return switch (actionType) {
            case CREATE -> "Tạo mới";
            case UPDATE -> "Cập nhật";
            case DELETE -> "Xóa";
            case ACTIVATE -> "Kích hoạt";
            case DEACTIVATE -> "Vô hiệu hóa";
            default -> actionType.name();
        };
    }

    /**
     * Log payment method action with automatic field comparison
     */
    @Transactional
    public void logPaymentMethodAction(PaymentMethod oldPaymentMethod, PaymentMethod newPaymentMethod,
                                       PaymentMethodHistory.ActionType actionType) {
        try {
            List<String> changedFields = new ArrayList<>();
            Map<String, Object> oldValues = new HashMap<>();
            Map<String, Object> newValues = new HashMap<>();

            // Compare basic fields
            compareAndAddField("name", oldPaymentMethod.getName(), newPaymentMethod.getName(),
                    changedFields, oldValues, newValues);
            compareAndAddField("providerId", oldPaymentMethod.getProviderId(), newPaymentMethod.getProviderId(),
                    changedFields, oldValues, newValues);
            compareAndAddField("bankCode", oldPaymentMethod.getBankCode(), newPaymentMethod.getBankCode(),
                    changedFields, oldValues, newValues);
            compareAndAddField("accountNumber", oldPaymentMethod.getAccountNumber(), newPaymentMethod.getAccountNumber(),
                    changedFields, oldValues, newValues);
            compareAndAddField("accountName", oldPaymentMethod.getAccountName(), newPaymentMethod.getAccountName(),
                    changedFields, oldValues, newValues);
            compareAndAddField("minAmount", oldPaymentMethod.getMinAmount(), newPaymentMethod.getMinAmount(),
                    changedFields, oldValues, newValues);
            compareAndAddField("maxAmount", oldPaymentMethod.getMaxAmount(), newPaymentMethod.getMaxAmount(),
                    changedFields, oldValues, newValues);
            compareAndAddField("isActive", oldPaymentMethod.getIsActive(), newPaymentMethod.getIsActive(),
                    changedFields, oldValues, newValues);

            // Compare addition_info JSON fields
            compareJsonFields(oldPaymentMethod.getAdditionInfo(), newPaymentMethod.getAdditionInfo(),
                    changedFields, oldValues, newValues);

            // Log the action with changes
            if (!changedFields.isEmpty()) {
                logPaymentMethodAction(newPaymentMethod, actionType, changedFields,
                        objectMapper.writeValueAsString(oldValues),
                        objectMapper.writeValueAsString(newValues));
            } else {
                logPaymentMethodAction(newPaymentMethod, actionType);
            }
        } catch (JsonProcessingException e) {
            log.error("Error processing payment method changes", e);
            // Fallback to simple logging
            logPaymentMethodAction(newPaymentMethod, actionType);
        }
    }

    /**
     * Compare two field values and add to change tracking if different
     */
    private void compareAndAddField(String fieldName, Object oldValue, Object newValue,
                                    List<String> changedFields, Map<String, Object> oldValues, Map<String, Object> newValues) {
        if (!Objects.equals(oldValue, newValue)) {
            changedFields.add(fieldName);
            oldValues.put(fieldName, oldValue);
            newValues.put(fieldName, newValue);
        }
    }

    /**
     * Compare JSON fields in addition_info
     */
    private void compareJsonFields(String oldJsonStr, String newJsonStr,
                                   List<String> changedFields, Map<String, Object> oldValues, Map<String, Object> newValues) {
        try {
            Map<String, Object> oldJson = parseJsonToMap(oldJsonStr);
            Map<String, Object> newJson = parseJsonToMap(newJsonStr);

            // Get all unique keys from both maps
            Set<String> allKeys = new HashSet<>();
            allKeys.addAll(oldJson.keySet());
            allKeys.addAll(newJson.keySet());

            // Compare each key
            for (String key : allKeys) {
                Object oldVal = oldJson.get(key);
                Object newVal = newJson.get(key);

                if (!Objects.equals(oldVal, newVal)) {
                    String fieldName = "additionInfo." + key;
                    changedFields.add(fieldName);
                    oldValues.put(fieldName, oldVal);
                    newValues.put(fieldName, newVal);
                }
            }
        } catch (Exception e) {
            log.warn("Error comparing JSON fields: {}", e.getMessage());
            // If JSON parsing fails, compare as strings
            compareAndAddField("additionInfo", oldJsonStr, newJsonStr, changedFields, oldValues, newValues);
        }
    }

    /**
     * Parse JSON string to Map, return empty map if null or invalid
     */
    private Map<String, Object> parseJsonToMap(String jsonStr) {
        if (jsonStr == null || jsonStr.trim().isEmpty()) {
            return new HashMap<>();
        }
        try {
            return objectMapper.readValue(jsonStr, new TypeReference<Map<String, Object>>() {
            });
        } catch (JsonProcessingException e) {
            log.warn("Failed to parse JSON: {}", jsonStr);
            return new HashMap<>();
        }
    }

    // Service Method
    @Override
    public Page<TransactionWithUserRes> getAllTransactions(String username, Long paymentMethodId, boolean isBonusFilter,
                                                           boolean isRemoveFilter,
                                                           LocalDate startDate, LocalDate endDate, Pageable pageable) {
        log.debug("Getting all transactions with filters - username: {}, paymentMethodId: {},  startDate: {}, endDate: {}",
                username, paymentMethodId, startDate, endDate);
        if (username != null) {
            username = username.toLowerCase().trim();
        }
        // Convert string types to TransactionType enums
        List<TransactionType> transactionTypes = new ArrayList<>();
        transactionTypes.add(TransactionType.Deposit);
        transactionTypes.add(TransactionType.Bonus);
        transactionTypes.add(TransactionType.Remove);

        // Convert dates to OffsetDateTime
        OffsetDateTime startDateTime = startDate != null ? CommonHelper.convertStartDate(startDate, ZONE_ID) : null;
        OffsetDateTime endDateTime = endDate != null ? CommonHelper.convertEndDate(endDate, ZONE_ID) : null;

        List<String> typeStrings = transactionTypes.stream()
                .map(TransactionType::name)
                .toList();

        // Get transactions using optimized single query
        List<Object[]> results = transactionRepository.findTransactionsWithFiltersSingleQuery(
                username, paymentMethodId, isBonusFilter, isRemoveFilter, typeStrings, startDateTime, endDateTime,
                TenantContext.getCurrentTenant(),
                pageable);

        // Extract total count and convert to TransactionWithUserRes
        long totalElements = 0;
        List<TransactionWithUserRes> transactionResponses = new ArrayList<>();

        for (Object[] row : results) {
            if (totalElements == 0 && row.length > 14) {
                totalElements = ((Number) row[14]).longValue(); // total_count from COUNT(*) OVER()
            }

            TransactionWithUserRes response = mapRowToTransactionWithUserRes(row);
            transactionResponses.add(response);
        }

        return new PageImpl<>(transactionResponses, pageable, totalElements);
    }

    private TransactionWithUserRes mapRowToTransactionWithUserRes(Object[] row) {
        TransactionWithUserRes response = new TransactionWithUserRes();

        response.setId(row[0] != null ? ((Number) row[0]).longValue() : null);
        response.setUserId(row[1] != null ? ((Number) row[1]).longValue() : null);
        response.setUsername((String) row[2]);
        response.setNote((String) row[3]);
        response.setOrderId(row[4] != null ? ((Number) row[4]).intValue() : null);
        response.setChange(row[5] != null ? new BigDecimal(row[5].toString()) : null);
        response.setBalance(row[6] != null ? new BigDecimal(row[6].toString()) : null);
        response.setOriginalAmount(row[7] != null ? new BigDecimal(row[7].toString()) : null);

        // Convert string to TransactionType enum
        if (row[8] != null) {
            try {
                response.setType(TransactionType.valueOf((String) row[8]));
            } catch (IllegalArgumentException e) {
                log.warn("Unknown transaction type: {}", row[8]);
            }
        }

        response.setSource((String) row[9]);
        response.setPaymentMethodId(row[10] != null ? ((Number) row[10]).longValue() : null);
        response.setPaymentMethodName((String) row[11]);
        response.setExternalTransactionId((String) row[12]);

        // Convert Timestamp to OffsetDateTime
        if (row[13] != null) {
            if (row[13] instanceof Timestamp) {
                response.setCreatedAt(((Timestamp) row[13]).toInstant().atZone(ZONE_ID).toOffsetDateTime());
            } else if (row[13] instanceof OffsetDateTime) {
                response.setCreatedAt((OffsetDateTime) row[13]);
            }
        }

        return response;
    }
}
