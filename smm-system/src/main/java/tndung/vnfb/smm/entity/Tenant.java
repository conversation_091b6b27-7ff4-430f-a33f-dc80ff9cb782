package tndung.vnfb.smm.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Formula;
import tndung.vnfb.smm.constant.enums.TenantStatus;
import tndung.vnfb.smm.dto.GeneralSettingsDto;
import tndung.vnfb.smm.entity.audit.AbstractCreatedAuditEntity;

import javax.persistence.*;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;

/**
 * Entity representing a tenant in the multi-tenant system
 */
@Entity
@Table(name = "tenant")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Tenant extends AbstractCreatedAuditEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 2L;

    @Id
    @Column(length = 36)
    private String id;


    @Column(unique = true, nullable = false)
    private String domain;
//
//    @Column(name = "subdomain")
//    private String subdomain;


    @Column(columnDefinition = "Numberic(1)")
    @Enumerated(EnumType.ORDINAL)
    private TenantStatus status = TenantStatus.New;

    @Column(name = "api_url")
    private String apiUrl;

    @Column(name = "site_url")
    private String siteUrl;

    @Column(name = "main")
    private Boolean main = false;


    @Column(name = "contact_email")
    private String contactEmail;

    private Boolean isDeleted = false;




    @Column(name = "subscription_start_date")
    private ZonedDateTime subscriptionStartDate;

    @Column(name = "subscription_end_date")
    private ZonedDateTime subscriptionEndDate;



   // @Formula("EXTRACT(DAY FROM (subscription_end_date - CURRENT_TIMESTAMP))")
    private Integer daysUntilExpiration;

    @Column(name = "auto_renewal")
    private Boolean autoRenewal = false;

    @Column(name = "renewal_notification_sent")
    private Boolean renewalNotificationSent = false;

    @Column(name = "last_renewal_date")
    private ZonedDateTime lastRenewalDate;


    @Column(name = "grace_period_days")
    private Integer gracePeriodDays = 7;

    @Column(name = "default_language", columnDefinition = "VARCHAR(4)")
    private String defaultLanguage = "vi";

    @Column(name = "available_languages", columnDefinition = "VARCHAR(50)")
    private String availableLanguages = "vi,en";

    @Column(name = "available_currencies", columnDefinition = "VARCHAR(200)")
    private String availableCurrencies = "USD";

    @Column(name = "last_currency_sync")
    private ZonedDateTime lastCurrencySync;

    @Column(name = "enable_affiliate")
    private Boolean enableAffiliate = false;

    @Column(name = "percentage_affiliate", precision = 5, scale = 2)
    private BigDecimal percentageAffiliate = BigDecimal.ZERO;

    @Column(name = "enable_discount_system")
    private Boolean enableDiscountSystem = false;

    @Column(name = "design_settings", columnDefinition = "TEXT")
    private String designSettings;

    @Column(name = "decimal_places")
    private Integer decimalPlaces = 2;

    @Column(name = "connection_settings", columnDefinition = "TEXT")
    private String connectionSettings;

    @Column(name = "average_time_settings", columnDefinition = "TEXT")
    private String averageTimeSettings;

    @Column(name = "general_settings", columnDefinition = "TEXT")
    private String generalSettings;

    // Sub-tenant hierarchy fields (Single level only: Parent -> Child)
    @Column(name = "parent_tenant_id", length = 36)
    private String parentTenantId;

    // Owner field - ID of the user who owns this tenant
    @Column(name = "owner_id")
    private Long ownerId;

    // JPA relationships for single-level hierarchy
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnore
    @JoinColumn(name = "parent_tenant_id", insertable = false, updatable = false)
    private Tenant parentTenant;

    @OneToMany(mappedBy = "parentTenant", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnore
    private List<Tenant> subTenants = new ArrayList<>();

    // JPA relationship for owner
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnore
    @JoinColumn(name = "owner_id", insertable = false, updatable = false)
    private GUser owner;

    // Helper methods
    public boolean isExpired() {
        return subscriptionEndDate != null && subscriptionEndDate.isBefore(ZonedDateTime.now());
    }

    public boolean isExpiringSoon(int days) {
        return subscriptionEndDate != null &&
                subscriptionEndDate.isBefore(ZonedDateTime.now().plusDays(days)) &&
                subscriptionEndDate.isAfter(ZonedDateTime.now());
    }

    public Integer getDaysUntilExpiration() {
        if( subscriptionEndDate == null) return null;
        return Math.toIntExact(ChronoUnit.DAYS.between(ZonedDateTime.now(), subscriptionEndDate));
    }

    // Sub-tenant helper methods (Single level hierarchy only)
    public boolean isParentTenant() {
        return parentTenantId == null;
    }

    public boolean isSubTenant() {
        return parentTenantId != null;
    }

    public void setAsSubTenant(String parentId) {
        this.parentTenantId = parentId;
    }

    public void setAsParentTenant() {
        this.parentTenantId = null;
    }

    public void setApiUrl() {
        this.apiUrl = String.format("https://%s/api/v1", this.domain);
    }

    /**
     * Calculate panel price for this tenant based on general settings and hierarchy
     * @param mainTenant The main tenant (for base price reference)
     * @return Calculated panel price
     */
    public BigDecimal calculatePanelPrice(Tenant mainTenant) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            GeneralSettingsDto generalSettings = parseGeneralSettings(objectMapper);
            BigDecimal percentage = generalSettings.getPanelPricePercentage();

            // Get base price
            BigDecimal basePrice = getBasePriceForCalculation(mainTenant, this.parentTenant, objectMapper);

            // Calculate final price: basePrice + (basePrice * percentage / 100)
            BigDecimal additionalAmount = basePrice.multiply(percentage).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP);
            return basePrice.add(additionalAmount);
        } catch (Exception e) {
            // Fallback to default price if calculation fails
            return new BigDecimal("7.7");
        }
    }

    /**
     * Calculate panel price for this tenant (simplified version for when you have all tenant data)
     * @return Calculated panel price
     */
    public BigDecimal calculatePanelPrice() {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            GeneralSettingsDto generalSettings = parseGeneralSettings(objectMapper);
            BigDecimal percentage = generalSettings.getPanelPricePercentage();

            // For main tenant, use base price from settings or default
            if (this.main != null && this.main) {
                BigDecimal basePrice = generalSettings.getBasePrice();
                BigDecimal additionalAmount = basePrice.multiply(percentage).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP);
                return basePrice.add(additionalAmount);
            }

            // For non-main tenants, return default calculation
            // Note: This simplified version assumes default base price
            // For accurate calculation with parent hierarchy, use calculatePanelPrice(mainTenant, parentTenant)
            BigDecimal defaultBasePrice = new BigDecimal("7.7");
            BigDecimal additionalAmount = defaultBasePrice.multiply(percentage).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP);
            return defaultBasePrice.add(additionalAmount);
        } catch (Exception e) {
            return new BigDecimal("7.7");
        }
    }

    /**
     * Parse general settings from JSON string
     */
    private GeneralSettingsDto parseGeneralSettings(ObjectMapper objectMapper) {
        if (this.generalSettings == null || this.generalSettings.trim().isEmpty()) {
            return GeneralSettingsDto.builder()
                    .panelPricePercentage(new BigDecimal("7.7"))
                    .basePrice(new BigDecimal("7.7"))
                    .build();
        }

        try {
            return objectMapper.readValue(this.generalSettings, GeneralSettingsDto.class);
        } catch (Exception e) {
            return GeneralSettingsDto.builder()
                    .panelPricePercentage(new BigDecimal("7.7"))
                    .basePrice(new BigDecimal("7.7"))
                    .build();
        }
    }

    /**
     * Get base price for calculation based on tenant hierarchy
     */
    private BigDecimal getBasePriceForCalculation(Tenant mainTenant, Tenant parentTenant, ObjectMapper objectMapper) {
        if (this.parentTenantId == null) {
            // Current tenant has no parent, get price from main tenant
            if (mainTenant != null) {
                GeneralSettingsDto mainSettings = mainTenant.parseGeneralSettings(objectMapper);
                if (this.id.equals(mainTenant.getId())) {
                    // This is the main tenant, use base price from settings
                    return mainSettings.getBasePrice();
                } else {
                    // This is a parent tenant, calculate based on main tenant
                    BigDecimal mainBasePrice = mainSettings.getBasePrice();
                    BigDecimal mainPercentage = mainSettings.getPanelPricePercentage();
                    BigDecimal additionalAmount = mainBasePrice.multiply(mainPercentage).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP);
                    return mainBasePrice.add(additionalAmount);
                }
            }
        } else {
            // Current tenant has parent, get price from parent tenant
            if (parentTenant != null) {
                return parentTenant.calculatePanelPrice(mainTenant);
            }
        }

        // Fallback to default
        return new BigDecimal("7.7");
    }
}