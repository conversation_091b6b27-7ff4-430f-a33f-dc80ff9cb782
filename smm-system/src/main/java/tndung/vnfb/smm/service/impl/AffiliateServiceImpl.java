package tndung.vnfb.smm.service.impl;

import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tndung.vnfb.smm.config.AuditContextHolder;
import tndung.vnfb.smm.config.TenantContext;
import tndung.vnfb.smm.constant.enums.TransactionType;
import tndung.vnfb.smm.dto.CommissionFilterDTO;
import tndung.vnfb.smm.dto.response.AffiliateRes;
import tndung.vnfb.smm.dto.response.CommissionRes;
import tndung.vnfb.smm.dto.response.ReferralRes;
import tndung.vnfb.smm.dto.response.ReferralStatsRes;
import tndung.vnfb.smm.entity.*;
import tndung.vnfb.smm.exception.IdErrorCode;
import tndung.vnfb.smm.exception.InvalidParameterException;
import tndung.vnfb.smm.repository.tenant.AffiliateRepository;
import tndung.vnfb.smm.repository.tenant.CommissionRepository;
import tndung.vnfb.smm.repository.tenant.ReferralRepository;
import tndung.vnfb.smm.repository.tenant.TransactionRepository;
import tndung.vnfb.smm.service.AffiliateService;
import tndung.vnfb.smm.repository.nontenant.GUserRepository;
import tndung.vnfb.smm.service.AuthenticationFacade;
import tndung.vnfb.smm.service.BalanceService;
import tndung.vnfb.smm.service.TenantService;
import tndung.vnfb.smm.helper.CommonHelper;
import lombok.extern.slf4j.Slf4j;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class AffiliateServiceImpl implements AffiliateService {
    private final AffiliateRepository affiliateRepo;
    private final ReferralRepository referralRepo;
    private final CommissionRepository commissionRepo;
    private final TransactionRepository transactionRepo;
    private final TenantService tenantService;
    private final GUserRepository gUserRepository;
    private final AuthenticationFacade authenticationFacade;
    private final BalanceService balanceService;

    private static final ZoneId ZONE_ID = AuditContextHolder.getUserZone();
    @PersistenceContext
    private EntityManager entityManager;
    @Override
    @Transactional
    public void createAffiliate(GUser user) {
        Optional<Tenant> tenantOptional = tenantService.findByTenantId(TenantContext.getCurrentTenant());

        if (tenantOptional.isEmpty()) {
            throw new InvalidParameterException(IdErrorCode.TENANT_NOT_FOUND);
        }

        Tenant tenant = tenantOptional.get();

        // Check if affiliate feature is enabled
//        if (tenant.getEnableAffiliate() == null || !tenant.getEnableAffiliate()) {
//            return; // Don't create affiliate if feature is disabled
//        }

        Affiliate affiliate = new Affiliate();
        affiliate.setUser(user);
        affiliate.setReferralCode(user.getUserName());
        affiliate.setReferralLink("https://"  +tenant.getDomain() + "/auth/register?ref=" + affiliate.getReferralCode());
        affiliateRepo.save(affiliate);
    }

    @Override
    public void createReferral(GUser user, String referralCode) {
        // Check if affiliate feature is enabled
        Optional<Tenant> tenantOptional = tenantService.findByTenantId(TenantContext.getWildcardTenant());
        if (tenantOptional.isPresent()) {
            Tenant tenant = tenantOptional.get();
            if (tenant.getEnableAffiliate() == null || !tenant.getEnableAffiliate()) {
                return; // Don't create referral if feature is disabled
            }
        }

        Affiliate affiliate = affiliateRepo.findByReferralCode(referralCode).orElseThrow(
                () -> new InvalidParameterException(IdErrorCode.REFERRAL_NOT_FOUND)
        );
        Referral referral = new Referral();
        referral.setAffiliate(affiliate);
        referral.setReferredUser(user);
        referralRepo.save(referral);
    }

    @Override
    public List<AffiliateRes> getAffiliates(Long userId) {
        return null;
    }

    @Override
    public Page<ReferralRes> getUserReferrals(Long userId, Pageable pageable) {
        Affiliate affiliate = affiliateRepo.findByUserId(userId)
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.REFERRAL_NOT_FOUND));

        Page<Referral> referrals = referralRepo.findByAffiliate(affiliate, pageable);
        return referrals.map(this::mapToReferralRes);
    }

    @Override
    public ReferralStatsRes getUserReferralStats(Long userId) {
        Affiliate affiliate = affiliateRepo.findByUserId(userId)
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.REFERRAL_NOT_FOUND));

        return buildReferralStats(affiliate);
    }

    @Override
    public Page<CommissionRes> getUserCommissions(Long userId, Pageable pageable) {
        final Affiliate affiliate = affiliateRepo.findByUserId(userId)
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.REFERRAL_NOT_FOUND));


        // Nếu cần phân trang, có thể thêm LIMIT và OFFSET vào native query
        List<CommissionRes> commissions = getCommissionDetails(affiliate.getId(), TenantContext.getCurrentTenant());

        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), commissions.size());

        List<CommissionRes> pageContent = commissions.subList(start, end);

        return new PageImpl<>(pageContent, pageable, commissions.size());
    }

    @Override
    public Page<CommissionRes> getMyCommissions(Pageable pageable) {
        GUser currentUser = authenticationFacade.getCurrentUser().orElseThrow(() ->
                new InvalidParameterException(IdErrorCode.USER_NOT_FOUND));

        Affiliate affiliate = affiliateRepo.findByUser(currentUser)
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.REFERRAL_NOT_FOUND));

        // Nếu cần phân trang, có thể thêm LIMIT và OFFSET vào native query
        List<CommissionRes> commissions = getCommissionDetails(affiliate.getId(), TenantContext.getSiteTenant());

        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), commissions.size());

        List<CommissionRes> pageContent = commissions.subList(start, end);

        return new PageImpl<>(pageContent, pageable, commissions.size());
    }


    @Override
    public Page<ReferralRes> getMyReferrals(Pageable pageable) {

        GUser currentUser = authenticationFacade.getCurrentUser().orElseThrow(() ->
                new InvalidParameterException(IdErrorCode.USER_NOT_FOUND));

        Affiliate affiliate = affiliateRepo.findByUser(currentUser)
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.REFERRAL_NOT_FOUND));

        Page<Referral> referrals = referralRepo.findByAffiliate(affiliate, pageable);
        return referrals.map(this::mapToReferralRes);
    }

    @Override
    public ReferralStatsRes getMyReferralStats() {
        GUser currentUser = authenticationFacade.getCurrentUser().orElseThrow(() ->
                new InvalidParameterException(IdErrorCode.USER_NOT_FOUND));

        Affiliate affiliate = affiliateRepo.findByUser(currentUser)
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.REFERRAL_NOT_FOUND));

        return buildReferralStats(affiliate);
    }

    @Override
    public String getMyAffiliateLink() {
        GUser currentUser = authenticationFacade.getCurrentUser().orElseThrow(() ->
                new InvalidParameterException(IdErrorCode.USER_NOT_FOUND));

        Affiliate affiliate = affiliateRepo.findByUser(currentUser)
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.REFERRAL_NOT_FOUND));

        return affiliate.getReferralLink();
    }




    public List<CommissionRes> getCommissionDetails(Long affiliateId, String tenantId) {
        List<Object[]> results = commissionRepo.findCommissionDetails(affiliateId, tenantId);

        return results.stream()
                .map(this::mapToCommissionDetailDTO)
                .collect(Collectors.toList());
    }

    private CommissionRes mapToCommissionDetailDTO(Object[] row) {
        CommissionRes res = new CommissionRes();
        res.setId(((Number) row[0]).longValue());
        res.setAffiliateId(((Number) row[1]).longValue());
        res.setTransactionId(row[2] != null ? ((Number) row[2]).longValue() : null);
        res.setCommissionAmount((BigDecimal) row[3]);
        res.setStatus(Commission.Status.valueOf((String) row[4]));
        res.setCreatedAt(((Timestamp) row[5]).toInstant().atOffset(ZoneOffset.UTC));
        res.setReferredUserName((String) row[6]);
        res.setTransactionNote((String) row[7]);
        return res;
    }



    @Override
    @Transactional
    public void processCommission(Long transactionId, BigDecimal amount) {
        // Find the transaction
        Optional<GTransaction> transactionOpt = transactionRepo.findById(transactionId);
        if (transactionOpt.isEmpty() || !TransactionType.Deposit.equals(transactionOpt.get().getType())) {
            return; // Only process deposit transactions
        }

        GTransaction transaction = transactionOpt.get();
        GUser user = gUserRepository.findById(transaction.getUserId())
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.USER_NOT_FOUND));

        // Find if this user was referred
        Optional<Referral> referralOpt = referralRepo.findByReferredUser(user);
        if (referralOpt.isEmpty()) {
            return; // User was not referred
        }

        Referral referral = referralOpt.get();
        Affiliate affiliate = referral.getAffiliate();

        // Get commission rate
        BigDecimal commissionRate = getEffectiveCommissionRate(affiliate.getUser());
        BigDecimal commissionAmount = amount.multiply(commissionRate).divide(BigDecimal.valueOf(100));

        // Create commission record
        Commission commission = new Commission();
        commission.setAffiliateId(affiliate.getId());
        commission.setTransactionId(transactionId);
        commission.setCommissionAmount(commissionAmount);
        commission.setStatus(Commission.Status.PENDING);

        commissionRepo.save(commission);
    }

    @Override
    @Transactional
    public void processCommissionPayment(Long commissionId) {
        // Find the commission
        Commission commission = commissionRepo.findById(commissionId)
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.COMMISSION_NOT_FOUND));

        // Check if commission is already paid
        if (Commission.Status.PAID.equals(commission.getStatus())) {
            throw new InvalidParameterException(IdErrorCode.COMMISSION_ALREADY_PAID);
        }

        // Find the affiliate user
        Affiliate affiliate = affiliateRepo.findById(commission.getAffiliateId())
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.AFFILIATE_NOT_FOUND));

        GUser affiliateUser = affiliate.getUser();

        // Add balance to affiliate user and create transaction
        String commissionNote = String.format("Commission payment: $%s", commission.getCommissionAmount());
        balanceService.addBalance(affiliateUser, commission.getCommissionAmount(), TransactionType.Affiliate, "BONUS", commissionNote);

        // Find the transaction that was just created
        Optional<GTransaction> transactionOpt = transactionRepo.findLatestByUserId(affiliateUser.getId());

        if (transactionOpt.isPresent()) {
            // Set transaction ID in commission
            commission.setTransactionId(transactionOpt.get().getId());
        }

        // Update commission status to PAID
        commission.setStatus(Commission.Status.PAID);
        commissionRepo.save(commission);

        log.info("Successfully processed commission payment: commissionId={}, affiliateUserId={}, amount={}",
                commissionId, affiliateUser.getId(), commission.getCommissionAmount());
    }

    private BigDecimal getEffectiveCommissionRate(GUser user) {
        // Use custom rate if set, otherwise use tenant default
        if (user.getCustomReferralRate() != null && user.getCustomReferralRate().compareTo(BigDecimal.ZERO) > 0) {
            return user.getCustomReferralRate();
        }

        Optional<Tenant> tenantOpt = tenantService.findByTenantId(TenantContext.getWildcardTenant());
        if (tenantOpt.isPresent() && tenantOpt.get().getPercentageAffiliate() != null) {
            return tenantOpt.get().getPercentageAffiliate();
        }

        return BigDecimal.ZERO;
    }

    private ReferralRes mapToReferralRes(Referral referral) {
        GUser user = referral.getReferredUser();
        ReferralRes res = new ReferralRes();
        res.setId(referral.getId());
        res.setUserName(user.getUserName());
        res.setEmail(user.getEmail());
        res.setAvatar(user.getAvatar());
        res.setStatus(user.getStatus().name());
        res.setLastLoginAt(user.getLastLoginAt());
        res.setCreatedAt(referral.getCreatedAt());
        res.setCustomReferralRate(user.getCustomReferralRate());
        // Note: Balance field removed for privacy - not displayed in affiliates component
        return res;
    }

    private CommissionRes mapToCommissionRes(Commission commission) {
        CommissionRes res = new CommissionRes();
        res.setId(commission.getId());
        res.setAffiliateId(commission.getAffiliateId());
        res.setTransactionId(commission.getTransactionId());
        res.setCommissionAmount(commission.getCommissionAmount());
        res.setStatus(commission.getStatus());
        res.setCreatedAt(commission.getCreatedAt());

        // Get additional info from transaction
        if (commission.getTransactionId() == null) return res;

        Optional<GTransaction> transactionOpt = transactionRepo.findById(commission.getTransactionId());
        if (transactionOpt.isPresent()) {
            GTransaction transaction = transactionOpt.get();
            res.setTransactionNote(transaction.getNote());

            // Get referred user name
            Optional<GUser> userOpt = gUserRepository.findById(transaction.getUserId());
            userOpt.ifPresent(user -> res.setReferredUserName(user.getUserName()));
        }

        return res;
    }

    private ReferralStatsRes buildReferralStats(Affiliate affiliate) {
        Long totalReferrals = referralRepo.countByAffiliateId(affiliate.getId());
        BigDecimal totalEarnings = commissionRepo.sumCommissionByAffiliateIdAndStatus(
                affiliate.getId(), Commission.Status.PAID);
        BigDecimal pendingCommissions = commissionRepo.sumCommissionByAffiliateIdAndStatus(
                affiliate.getId(), Commission.Status.PENDING);

        if (totalEarnings == null) totalEarnings = BigDecimal.ZERO;
        if (pendingCommissions == null) pendingCommissions = BigDecimal.ZERO;

        BigDecimal customRate = affiliate.getUser().getCustomReferralRate();
        BigDecimal defaultRate = getDefaultCommissionRate();

        ReferralStatsRes stats = new ReferralStatsRes();
        stats.setTotalReferrals(totalReferrals);
        stats.setTotalEarnings(totalEarnings);
        stats.setPendingCommissions(pendingCommissions);
        stats.setPaidCommissions(totalEarnings);
        stats.setCustomReferralRate(customRate);
        stats.setDefaultReferralRate(defaultRate);
        stats.setReferralLink(affiliate.getReferralLink());
        stats.setReferralCode(affiliate.getReferralCode());

        return stats;
    }

    private BigDecimal getDefaultCommissionRate() {
        Optional<Tenant> tenantOpt = tenantService.findByTenantId(TenantContext.getWildcardTenant());
        if (tenantOpt.isPresent() && tenantOpt.get().getPercentageAffiliate() != null) {
            return tenantOpt.get().getPercentageAffiliate();
        }
        return BigDecimal.ZERO;
    }

    @Override
    public Page<CommissionRes> getAllCommissions(CommissionFilterDTO filter) {

        OffsetDateTime startDateTime = CommonHelper.convertStartDate(filter.getStartDate(), ZONE_ID);
        OffsetDateTime endDateTime = CommonHelper.convertEndDate(filter.getEndDate(), ZONE_ID);
        log.info("Getting commissions with filter: {}", filter);

        // Validate input
        if (filter.getSize() > 100) {
            filter.setSize(100); // Giới hạn size tối đa
        }

        int offset = filter.getPage() * filter.getSize();

        // Get data
        List<Object[]> results = commissionRepo.findCommissionsWithPagination(
                startDateTime,
                endDateTime,
                filter.getUserName(),
                filter.getStatus(),
                TenantContext.getCurrentTenant(),
                filter.getSize(),
                offset
        );

        // Get total count
        Long totalCount = commissionRepo.countCommissionsWithFilters(
                startDateTime,
                endDateTime,
                filter.getUserName(),
                filter.getStatus(),
                TenantContext.getCurrentTenant()
        );

        // Convert to DTO
        List<CommissionRes> commissions = results.stream()
                .map(this::mapToCommissionResponseDTO)
                .collect(Collectors.toList());

        return new PageImpl<>(
                commissions,
                PageRequest.of(filter.getPage(), filter.getSize()),
                totalCount
        );
    }

    private CommissionRes mapToCommissionResponseDTO(Object[] row) {
        return CommissionRes.builder()
                .id(((Number) row[0]).longValue())
                .affiliateId(row[1] != null ? ((Number) row[1]).longValue() : null)
                .transactionId(row[2] != null ? ((Number) row[2]).longValue() : null)
                .commissionAmount((BigDecimal) row[3])
                .status(Commission.Status.valueOf((String) row[4]))
                .createdAt(row[5] != null ? ((Timestamp) row[5]).toInstant().atOffset(ZoneOffset.UTC) : null)
                .referredUserName((String) row[6])
                .referrerName((String) row[7])

                .transactionNote((String) row[8])
                .build();
    }

//    private List<CommissionRes> getAllCommissionDetails(String tenantId, String status, String startDate, String endDate) {
//        // Build dynamic query based on filters
//        StringBuilder queryBuilder = new StringBuilder();
//        queryBuilder.append("""
//            SELECT
//                c.id,
//                c.affiliate_id,
//                c.transaction_id,
//                c.commission_amount,
//                c.status,
//                c.created_at,
//                gu.user_name as referred_user_name,
//                gt.note as transaction_note,
//                referrer.user_name as referrer_name
//            FROM commission c
//            LEFT JOIN g_transaction gt ON c.transaction_id = gt.id
//            LEFT JOIN g_user gu ON gt.user_id = gu.id
//            LEFT JOIN affiliate a ON c.affiliate_id = a.id
//            LEFT JOIN g_user referrer ON a.user_id = referrer.id
//            WHERE c.tenant_id = :tenantId
//            """);
//
//        // Add status filter
//        if (status != null && !status.isEmpty() && !"Tất cả trạng thái".equals(status)) {
//            queryBuilder.append(" AND c.status = :status");
//        }
//
//        // Add date range filters
//        if (startDate != null && !startDate.isEmpty()) {
//            queryBuilder.append(" AND DATE(c.created_at) >= :startDate");
//        }
//        if (endDate != null && !endDate.isEmpty()) {
//            queryBuilder.append(" AND DATE(c.created_at) <= :endDate");
//        }
//
//        queryBuilder.append(" ORDER BY c.created_at DESC");
//
//        Query query = entityManager.createNativeQuery(queryBuilder.toString());
//        query.setParameter("tenantId", tenantId);
//
//        if (status != null && !status.isEmpty() && !"Tất cả trạng thái".equals(status)) {
//            query.setParameter("status", status);
//        }
//        if (startDate != null && !startDate.isEmpty()) {
//            query.setParameter("startDate", startDate);
//        }
//        if (endDate != null && !endDate.isEmpty()) {
//            query.setParameter("endDate", endDate);
//        }
//
//        @SuppressWarnings("unchecked")
//        List<Object[]> results = query.getResultList();
//
//        return results.stream().map(row -> {
//            CommissionRes res = new CommissionRes();
//            res.setId(((Number) row[0]).longValue());
//            res.setAffiliateId(((Number) row[1]).longValue());
//            res.setTransactionId(row[2] != null ? ((Number) row[2]).longValue() : null);
//            res.setCommissionAmount((BigDecimal) row[3]);
//            res.setStatus(Commission.Status.valueOf((String) row[4]));
//            res.setCreatedAt(((Timestamp) row[5]).toInstant().atOffset(ZoneOffset.UTC));
//            res.setReferredUserName((String) row[6]);
//            res.setTransactionNote((String) row[7]);
//            // Add referrer name for admin view
//            if (row.length > 8) {
//                res.setReferrerName((String) row[8]);
//            }
//            return res;
//        }).collect(Collectors.toList());
//    }
}
