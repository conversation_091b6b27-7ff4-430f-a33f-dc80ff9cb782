package tndung.vnfb.smm.repository.tenant;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import tndung.vnfb.smm.entity.Commission;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.List;

public interface CommissionRepository extends TenantAwareRepository<Commission, Long> {

    @Query("SELECT c FROM Commission c WHERE c.affiliateId = :affiliateId AND c.tenantId = :#{T(tndung.vnfb.smm.config.TenantContext).getWildcardTenant()} ORDER BY c.createdAt DESC")
    Page<Commission> findByAffiliateId(@Param("affiliateId") Long affiliateId, Pageable pageable);

    @Query("SELECT SUM(c.commissionAmount) FROM Commission c WHERE c.affiliateId = :affiliateId AND c.status = :status AND c.tenantId = :#{T(tndung.vnfb.smm.config.TenantContext).getWildcardTenant()}")
    BigDecimal sumCommissionByAffiliateIdAndStatus(@Param("affiliateId") Long affiliateId, @Param("status") Commission.Status status);

    @Query("SELECT c FROM Commission c WHERE c.affiliateId = :affiliateId AND c.status = :status AND c.tenantId = :#{T(tndung.vnfb.smm.config.TenantContext).getWildcardTenant()}")
    List<Commission> findByAffiliateIdAndStatus(@Param("affiliateId") Long affiliateId, @Param("status") Commission.Status status);


    @Query(value = """
            
                        SELECT * FROM (
                SELECT DISTINCT ON (c.id)
                    c.id,
                    c.affiliate_id,
                    c.transaction_id,
                    c.commission_amount,
                    c.status,
                    c.created_at,
                    referred_user.user_name as referred_user_name,
                    referrer_user.user_name as referrer_name,
                    gt.note as transaction_note
                FROM commission c
                LEFT JOIN affiliate a ON c.affiliate_id = a.id
                LEFT JOIN referral r ON a.id = r.affiliate_id
                LEFT JOIN g_user referred_user ON r.referred_user_id = referred_user.id
                LEFT JOIN g_user referrer_user ON a.user_id = referrer_user.id
                LEFT JOIN g_transaction gt ON c.transaction_id = gt.id
                WHERE c.tenant_id = :tenantId\s
                AND c.affiliate_id = :affiliateId
                ORDER BY c.id, c.created_at DESC
            ) subquery
            ORDER BY created_at DESC
            """, nativeQuery = true)
    List<Object[]> findCommissionDetails(
            @Param("affiliateId") Long affiliateId,
            @Param("tenantId") String tenantId
    );



    @Query(value = """
    SELECT DISTINCT
        c.id,
        c.affiliate_id,
        c.transaction_id,
        c.commission_amount,
        c.status,
        c.created_at,
        FIRST_VALUE(referred_user.user_name) OVER (PARTITION BY c.id ORDER BY r.created_at ASC) as referred_user_name,
        referrer.user_name as referrer_name,
        COALESCE(t.note, '') as transaction_note
    FROM commission c
    LEFT JOIN affiliate a ON c.affiliate_id = a.id
    LEFT JOIN g_user referrer ON a.user_id = referrer.id
    LEFT JOIN referral r ON a.id = r.affiliate_id
    LEFT JOIN g_user referred_user ON r.referred_user_id = referred_user.id
    LEFT JOIN g_transaction t ON c.transaction_id = t.id
    WHERE 1=1
    AND (CAST(:startDate AS timestamp) IS NULL OR c.created_at >= :startDate)
    AND (CAST(:endDate AS timestamp) IS NULL OR c.created_at <= :endDate)
    AND (:userName IS NULL OR 
         LOWER(referred_user.user_name) LIKE LOWER(CONCAT('%', :userName, '%')) OR
         LOWER(referrer.user_name) LIKE LOWER(CONCAT('%', :userName, '%')))
    AND (:status IS NULL OR c.status = :status)
    AND c.tenant_id = :tenantId
    ORDER BY 
        c.transaction_id DESC,
        c.created_at DESC,
        c.id DESC
    LIMIT :limit OFFSET :offset
    """, nativeQuery = true)
    List<Object[]> findCommissionsWithPagination(
            @Param("startDate") OffsetDateTime startDate,
            @Param("endDate") OffsetDateTime endDate,
            @Param("userName") String userName,
            @Param("status") String status,
            @Param("tenantId") String tenantId,
            @Param("limit") int limit,
            @Param("offset") int offset
    );

    @Query(value = """
    SELECT COUNT(DISTINCT c.id)
    FROM commission c
    LEFT JOIN affiliate a ON c.affiliate_id = a.id
    LEFT JOIN g_user referrer ON a.user_id = referrer.id
    LEFT JOIN referral r ON a.id = r.affiliate_id
    LEFT JOIN g_user referred_user ON r.referred_user_id = referred_user.id
    LEFT JOIN g_transaction t ON c.transaction_id = t.id
    WHERE 1=1
    AND (CAST(:startDate AS timestamp) IS NULL OR c.created_at >= :startDate)
    AND (CAST(:endDate AS timestamp) IS NULL OR c.created_at <= :endDate)
    AND (:userName IS NULL OR 
         LOWER(referred_user.user_name) LIKE LOWER(CONCAT('%', :userName, '%')) OR
         LOWER(referrer.user_name) LIKE LOWER(CONCAT('%', :userName, '%')))
    AND (:status IS NULL OR c.status = :status)
    AND c.tenant_id = :tenantId
    """, nativeQuery = true)
    Long countCommissionsWithFilters(
            @Param("startDate") OffsetDateTime startDate,
            @Param("endDate") OffsetDateTime endDate,
            @Param("userName") String userName,
            @Param("status") String status,
            @Param("tenantId") String tenantId
    );
}
