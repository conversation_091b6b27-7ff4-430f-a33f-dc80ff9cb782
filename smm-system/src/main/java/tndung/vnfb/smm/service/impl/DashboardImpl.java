package tndung.vnfb.smm.service.impl;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import tndung.vnfb.smm.config.AuditContextHolder;
import tndung.vnfb.smm.config.TenantContext;
import tndung.vnfb.smm.dto.response.DashboardRes;
import tndung.vnfb.smm.dto.response.RevenueRes;
import tndung.vnfb.smm.dto.response.StatisticRes;
import tndung.vnfb.smm.repository.nontenant.GUserRepository;
import tndung.vnfb.smm.repository.tenant.OrderRepository;
import tndung.vnfb.smm.repository.tenant.TicketRepository;
import tndung.vnfb.smm.repository.tenant.TransactionRepository;
import tndung.vnfb.smm.service.DashboardService;
import tndung.vnfb.smm.helper.CommonHelper;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.*;
import java.time.format.DateTimeFormatter;

import static tndung.vnfb.smm.constant.Common.SCALE_DEFAULT;

@RequiredArgsConstructor
@Service
public class DashboardImpl implements DashboardService {

    private final OrderRepository orderRepository;
    private final TicketRepository ticketRepository;

    private final GUserRepository userRepository;
    private final TransactionRepository transactionRepository;

    @Override
    public DashboardRes getDashboardStats(LocalDate startDate, LocalDate endDate) {

        // Lấy ZoneId từ người dùng hiện tại hoặc mặc định sử dụng UTC
        ZoneId zoneId = AuditContextHolder.getUserZone();
        String currentTenant = TenantContext.getCurrentTenant();
        // Nếu không có ngày được chỉ định, mặc định lấy tháng hiện tại
        if (startDate == null || endDate == null) {
            YearMonth currentMonth = YearMonth.now();
            startDate = currentMonth.atDay(1);
            endDate = currentMonth.atEndOfMonth();
        }

        // Chuyển đổi LocalDate thành OffsetDateTime
        OffsetDateTime startDateTime = CommonHelper.convertStartDate(startDate, zoneId);
        OffsetDateTime endDateTime = CommonHelper.convertEndDate(endDate, zoneId);

        // Tính toán thời gian cho khoảng thời gian trước đó để so sánh
        LocalDate previousStartDate;
        LocalDate previousEndDate;

        // Nếu khoảng thời gian là một tháng đầy đủ
        if (startDate.getDayOfMonth() == 1 && endDate.getDayOfMonth() == endDate.lengthOfMonth()) {
            YearMonth previousMonth = YearMonth.from(startDate).minusMonths(1);
            previousStartDate = previousMonth.atDay(1);
            previousEndDate = previousMonth.atEndOfMonth();
        } else {
            // Nếu khoảng thời gian tùy chỉnh, lấy khoảng thời gian trước đó có cùng độ dài
            long days = endDate.toEpochDay() - startDate.toEpochDay() + 1;
            previousEndDate = startDate.minusDays(1);
            previousStartDate = previousEndDate.minusDays(days - 1);
        }

        OffsetDateTime previousStartDateTime = CommonHelper.convertStartDate(previousStartDate, zoneId);
        OffsetDateTime previousEndDateTime = CommonHelper.convertEndDate(previousEndDate, zoneId);

        // Lấy thống kê cho mỗi loại dữ liệu
        StatisticRes orders = getStatistic(
                orderRepository.countOrdersBetweenDates(startDateTime, endDateTime, currentTenant),
                orderRepository.countOrdersBetweenDates(previousStartDateTime, previousEndDateTime, currentTenant)
        );

        StatisticRes pendingTickets = getStatistic(
                ticketRepository.countPendingTicketsBetweenDates(startDateTime, endDateTime,currentTenant),
                ticketRepository.countPendingTicketsBetweenDates(previousStartDateTime, previousEndDateTime, currentTenant)
        );

        StatisticRes activeUsers = getStatistic(
                userRepository.countActiveUsersBetweenDates(startDateTime, endDateTime),
                userRepository.countActiveUsersBetweenDates(previousStartDateTime, previousEndDateTime)
        );

        // Tính toán doanh thu (revenue) từ các giao dịch deposit
        BigDecimal currentRevenue = transactionRepository.sumRevenueBetweenDates(startDateTime, endDateTime, currentTenant);
        BigDecimal previousRevenue = transactionRepository.sumRevenueBetweenDates(previousStartDateTime, previousEndDateTime, currentTenant);

        if (currentRevenue == null) {
            currentRevenue = BigDecimal.ZERO;
        }

        if (previousRevenue == null) {
            previousRevenue = BigDecimal.ZERO;
        }

        RevenueRes revenue = getRevenueStatistic(currentRevenue, previousRevenue);

        // Định dạng khoảng thời gian
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
        String periodText = formatter.format(startDate) + " - " + formatter.format(endDate);

        return DashboardRes.builder()
                .orders(orders)
                .pendingTickets(pendingTickets)
                .activeUsers(activeUsers)
                .revenue(revenue)
                .period(periodText)
                .build();
    }


    private StatisticRes getStatistic(Long currentCount, Long previousCount) {
        double percentChange = 0;

        if (previousCount != null && previousCount > 0) {
            percentChange = ((double) (currentCount - previousCount) / previousCount) * 100;
        }

        return StatisticRes.builder()
                .count(currentCount)

                .previousCount(previousCount)
                .percentChange(Math.round(percentChange * 100) / 100.0) // Làm tròn 2 chữ số thập phân
                .build();
    }


    private RevenueRes getRevenueStatistic(BigDecimal currentRevenue, BigDecimal previousRevenue) {
        double percentChange = 0;

        if (previousRevenue != null && previousRevenue.compareTo(BigDecimal.ZERO) > 0) {
            percentChange = currentRevenue.subtract(previousRevenue)
                    .divide(previousRevenue, SCALE_DEFAULT, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100))
                    .doubleValue();
        }

        return RevenueRes.builder()
                .currentRevenue(currentRevenue)
                .previousRevenue(previousRevenue)
                .percentChange(Math.round(percentChange * 100) / 100.0) // Làm tròn 2 chữ số thập phân
                .build();
    }
}
