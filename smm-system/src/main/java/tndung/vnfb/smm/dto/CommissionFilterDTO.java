package tndung.vnfb.smm.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.RequestParam;

import java.time.LocalDate;
import java.time.OffsetDateTime;

// DTO cho request filter
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CommissionFilterDTO {

    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    LocalDate startDate;
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    LocalDate endDate;
    private String userName; // Có thể tìm theo referred, referral hoặc referrer
    private String status;
    private int page = 0;
    private int size = 20;
    private String sortBy = "createdAt";
    private String sortDir = "asc";
}
