package tndung.vnfb.smm.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import tndung.vnfb.smm.anotation.TenantCheck;
import tndung.vnfb.smm.dto.ApiResponseEntity;
import tndung.vnfb.smm.dto.request.*;
import tndung.vnfb.smm.dto.response.*;
import tndung.vnfb.smm.service.GUserService;
import tndung.vnfb.smm.service.LoginHistoryService;

import javax.validation.Valid;
import javax.validation.groups.Default;
import java.util.List;


@RestController
@RequestMapping("/v1/users")
@RequiredArgsConstructor
public class GUserController {


    private final GUserService gUserService;
    private final LoginHistoryService loginHistoryService;


    @PostMapping("/register")
    public ApiResponseEntity<GUserRes> create(@RequestBody @Valid UserReq userReq) {
        return ApiResponseEntity.success(gUserService.create(userReq, null));
    }

    @PutMapping("/{id}/edit")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_CHILD_PANEL')")
    @TenantCheck
    public ApiResponseEntity<GUserRes> superEdit(@PathVariable Long id, @RequestBody @Validated(Default.class) EditUserReq userReq) {
        return ApiResponseEntity.success(gUserService.superEdit(id, userReq));
    }

    @PostMapping("/{userId}/services/custom-discount")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_CHILD_PANEL')")
    @TenantCheck
    public ApiResponseEntity<SpecialPriceRes> addCustomDiscountServices
            (@PathVariable Long userId, @RequestBody @Valid CustomDiscountServiceReq customDiscount) {
        return ApiResponseEntity.success(gUserService.addCustomDiscount(userId, customDiscount));
    }

    @GetMapping("/{userId}/services/custom-discount")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_CHILD_PANEL')")
    @TenantCheck
    public ApiResponseEntity<List<SpecialPriceRes>> getCustomDiscountByUser(@PathVariable Long userId) {
        return ApiResponseEntity.success(gUserService.getCustomDiscountByUser(userId));
    }

    @GetMapping("/services/{serviceId}/custom-discount")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_CHILD_PANEL')")
    @TenantCheck
    public ApiResponseEntity<List<SpecialPriceRes>> getCustomDiscountByService(@PathVariable Long serviceId) {
        return ApiResponseEntity.success(gUserService.getCustomDiscountByService(serviceId));
    }

    @GetMapping("/services/custom-discount/count")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_CHILD_PANEL')")
    @TenantCheck
    public ApiResponseEntity<List<SpecialPriceCountRes>> getCountCustomDiscountByService() {
        return ApiResponseEntity.success(gUserService.getCountCustomDiscountByService());
    }

    @PutMapping("/services/custom-discount/{id}")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_CHILD_PANEL')")
    @TenantCheck
    public ApiResponseEntity<SpecialPriceRes> editCustomDiscount
            (@PathVariable Long id, @RequestBody @Valid CustomDiscountServiceReq customDiscount) {

        return ApiResponseEntity.success(gUserService.editCustomDiscount(id, customDiscount));
    }

    @DeleteMapping("/{userId}/services/custom-discount")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_CHILD_PANEL')")
    @TenantCheck
    public ApiResponseEntity<String> deleteCustomDiscountByUser
            (@PathVariable Long userId) {
        gUserService.deleteCustomDiscountByUser(userId);
        return ApiResponseEntity.success();
    }
    @DeleteMapping("/services/{serviceId}/custom-discount")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_CHILD_PANEL')")
    @TenantCheck
    public ApiResponseEntity<String> deleteCustomDiscountByService
            (@PathVariable Long serviceId) {
        gUserService.deleteCustomDiscountByService(serviceId);
        return ApiResponseEntity.success();
    }

    @DeleteMapping("/services/custom-discount/{id}")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_CHILD_PANEL')")
    @TenantCheck
    public ApiResponseEntity<String> deleteCustomDiscount
            (@PathVariable Long id) {
        gUserService.deleteCustomDiscount(id);
        return ApiResponseEntity.success();
    }


    @PatchMapping("/{id}/custom-referral")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_CHILD_PANEL')")
    @TenantCheck
    public ApiResponseEntity<GUserRes> addCustomReferral(@PathVariable Long id, @RequestBody @Valid CustomReferralReq req) {
        return ApiResponseEntity.success(gUserService.customReferral(id, req));
    }


    @PatchMapping("/{id}/custom-discount")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_CHILD_PANEL')")
    @TenantCheck
    public ApiResponseEntity<GUserRes> addAllCustomDiscount(@PathVariable Long id, @RequestBody @Valid CustomDiscountReq customDiscount) {
        return ApiResponseEntity.success(gUserService.addAllCustomDiscount(id, customDiscount));
    }

    @GetMapping("/search")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_CHILD_PANEL')")
    @TenantCheck
    public ApiResponseEntity<Page<GUserSuperRes>> search(UserSearchReq req, Pageable pageable) {
        return ApiResponseEntity.success(gUserService.search(req, pageable));
    }

    @GetMapping("/{id}/detail")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_CHILD_PANEL')")
    @TenantCheck
    public ApiResponseEntity<GUserSuperRes> detail(@PathVariable Long id) {
        return ApiResponseEntity.success(gUserService.getDetailUser(id));
    }


    @PutMapping("/{id}/add-fund")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_CHILD_PANEL')")
    @TenantCheck
    public ApiResponseEntity<TransactionRes> addFund(@PathVariable Long id, @RequestBody @Valid AddFundReq req) {
        return ApiResponseEntity.success(gUserService.addFund(id, req));
    }


    @PutMapping("/{id}/active")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_CHILD_PANEL')")
    @TenantCheck
    public ApiResponseEntity<GUserRes> active(@PathVariable Long id) {
        return ApiResponseEntity.success(gUserService.active(id));
    }

    @PutMapping("/{id}/reset-password")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_CHILD_PANEL')")
    @TenantCheck
    public ApiResponseEntity<GUserPasswordRes> resetPassword(@PathVariable Long id) {
        return ApiResponseEntity.success(gUserService.resetPassword(id));
    }

    @PutMapping("/{id}/deactivate")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_CHILD_PANEL')")
    @TenantCheck
    public ApiResponseEntity<GUserRes> deactivate(@PathVariable Long id) {
        return ApiResponseEntity.success(gUserService.deactivate(id));
    }


    @GetMapping("/{id}/login-history")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_CHILD_PANEL')")
    @TenantCheck
    public ApiResponseEntity<List<LoginHistoryRes>> getLoginHistories(@PathVariable Long id) {
        return ApiResponseEntity.success(loginHistoryService.findLoginHistoriesById(id));
    }
  



    @GetMapping("/{id}/transactions")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_CHILD_PANEL')")
    @TenantCheck
    public ApiResponseEntity<Page<TransactionRes>> getTransactions(@PathVariable Long id, TransactionSearchReq req, Pageable pageable) {
        return ApiResponseEntity.success(gUserService.getTransactionsByUser(id, req, pageable));
    }

    @GetMapping("/{userId}/transactions/summary")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_CHILD_PANEL')")
    @TenantCheck
    public ApiResponseEntity<SummaryTransaction> summaryTransactions(@PathVariable Long userId) {
        return ApiResponseEntity.success(gUserService.summaryTransactions(userId));
    }

    // ------ ROLE USER ------------
    @GetMapping("/me/transactions")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_CHILD_PANEL', 'ROLE_USER')")
    public ApiResponseEntity<Page<MyTransactionRes>> getMyTransactions(TransactionSearchReq req, Pageable pageable) {
        return ApiResponseEntity.success(gUserService.getMyTransactions(req, pageable));
    }
    @GetMapping("/me/language")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_CHILD_PANEL',  'ROLE_USER')")
    public ApiResponseEntity<LanguageRes> getLanguage() {
        return ApiResponseEntity.success(gUserService.getLanguage());
    }

    @PatchMapping("/me/language")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_CHILD_PANEL',  'ROLE_USER')")
    public ApiResponseEntity<LanguageRes> setLanguage(@RequestBody LanguageReq req) {
        return ApiResponseEntity.success(gUserService.setLanguage(req));
    }

    @PatchMapping("/me/currency")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_CHILD_PANEL','ROLE_USER')")
    public ApiResponseEntity<GUserRes> setCurrency(@RequestBody CurrencyReq req) {
        return ApiResponseEntity.success(gUserService.setCurrency(req));
    }

    @PatchMapping("/me/time-zone")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_CHILD_PANEL', 'ROLE_USER')")
    public ApiResponseEntity<GUserRes> setTimeZone(@RequestBody TimezoneReq req) {
        return ApiResponseEntity.success(gUserService.setTimezone(req));
    }

    @GetMapping("/me/transactions/summary")
    @PreAuthorize("hasAnyRole('ROLE_USER' ,'ROLE_PANEL', 'ROLE_CHILD_PANEL')")
    public ApiResponseEntity<MySummaryTransaction> summaryMyTransactions() {
        return ApiResponseEntity.success(gUserService.summaryMyTransactions());
    }

    @PutMapping("/me")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_CHILD_PANEL',  'ROLE_USER')")
    public ApiResponseEntity<GUserRes> edit(@RequestBody @Validated(OnMe.class) EditUserReq req) {
        return ApiResponseEntity.success(gUserService.edit(req));
    }

    @GetMapping("/me")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_CHILD_PANEL',  'ROLE_USER')")
    public ApiResponseEntity<GUserRes> getInfo() {
        return ApiResponseEntity.success(gUserService.getInfo());
    }

    @GetMapping("/me/api-key")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_CHILD_PANEL',  'ROLE_USER')")
    public ApiResponseEntity<ApiKeyUserRes> getApiKey() {
        return ApiResponseEntity.success(gUserService.getApiKey());
    }

    @GetMapping("/me/login-history")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_CHILD_PANEL',  'ROLE_USER')")
    public ApiResponseEntity<List<LoginHistoryRes>> getMyLoginHistory() {
        return ApiResponseEntity.success(loginHistoryService.findMyLoginHistory());
    }

    @PutMapping("/me/change-password")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_CHILD_PANEL',  'ROLE_USER')")
    public ApiResponseEntity<String> changePassword(@RequestBody @Valid UserChangePassReq req) {
        gUserService.changePassword(req);
        return ApiResponseEntity.success();
    }

    @PutMapping("/me/api-key")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_CHILD_PANEL', 'ROLE_USER')")
    public ApiResponseEntity<ApiKeyUserRes> generateApiKey() {
        return ApiResponseEntity.success(gUserService.generateApiKey());
    }

    @PostMapping("/me/verify-email")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_CHILD_PANEL', 'ROLE_USER')")
    public ApiResponseEntity<String> verifyEmail() {
        gUserService.sendEmailVerification();
        return ApiResponseEntity.success("Verification email sent successfully");
    }

    @PostMapping("/verify-email")
    public ApiResponseEntity<String> confirmEmailVerification(@RequestParam String token) {
        gUserService.confirmEmailVerification(token);
        return ApiResponseEntity.success("Email verified successfully");
    }

    @PostMapping("/{userId}/deposit")
    @PreAuthorize("hasAnyRole('ROLE_PANEL', 'ROLE_CHILD_PANEL')")
    @TenantCheck
    public ApiResponseEntity<GUserRes> deposit(@PathVariable Long userId, @RequestBody DepositReq req) {
        return ApiResponseEntity.success(gUserService.deposit(userId, req));
    }


}
