import { Component, OnInit, ViewChild, ElementRef, AfterViewInit } from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { Router, RouterModule, ActivatedRoute } from '@angular/router';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { AuthService } from '../../core/services/auth.service';
import { ToastService } from '../../core/services/toast.service';
import { NotifyType } from '../../constant/notify-type';
import { catchError, finalize, throwError } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { ConfigService } from '../../core/services/config.service';
import { MfaStateService } from '../../core/services/mfa-state.service';

@Component({
  selector: 'app-mfa',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, RouterModule, TranslateModule],
  templateUrl: './mfa.component.html',
  styleUrl: './mfa.component.css'
})
export class MfaComponent implements OnInit, AfterViewInit {
  // Form definition with validation
  mfaForm = new FormGroup({
    code: new FormControl('', [Validators.required, Validators.minLength(6), Validators.maxLength(6)])
  });

  @ViewChild('digit1') digit1!: ElementRef;

  isLoading = false;
  mfaError: string | null = null;
  returnUrl: string = '/panel/dashboard';
  digitInputs: HTMLInputElement[] = [];

  // Variables to store MFA data
  private userName: string = '';
  private password: string = '';
  private loginFirstFactor: string = '';

  constructor(
    private authService: AuthService,
    private router: Router,
    private route: ActivatedRoute,
    private toastService: ToastService,
    private http: HttpClient,
    private configService: ConfigService,
    private mfaStateService: MfaStateService
  ) {
    // Get MFA data from the secure state service
    this.userName = this.mfaStateService.username;
    this.password = this.mfaStateService.password;
    this.loginFirstFactor = this.mfaStateService.loginFirstFactor;

    if (this.mfaStateService.returnUrl) {
      this.returnUrl = this.mfaStateService.returnUrl;
    }

    // If we don't have the required data, redirect back to login
    if (!this.userName || !this.password || !this.loginFirstFactor) {
      console.log('MFA Component: Missing required data, redirecting to login page');
      this.toastService.showToast('MFA verification session expired. Please log in again.', NotifyType.WARNING);
      this.router.navigate(['/auth/login']);
    }
  }

  ngOnInit(): void {
    // Get return url from route parameters or default to '/dashboard/new'
    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/panel/dashboard';

    // Double-check that we still have the required data
    // This handles cases where the component is loaded directly without going through the login flow
    if (!this.userName || !this.password || !this.loginFirstFactor) {
      console.log('MFA Component (ngOnInit): Missing required data, redirecting to login page');
      this.toastService.showToast('MFA verification session expired. Please log in again.', NotifyType.WARNING);
      this.router.navigate(['/auth/login']);
    }
  }

  ngAfterViewInit(): void {
    // Focus on the first input after view is initialized
    setTimeout(() => {
      if (this.digit1 && this.digit1.nativeElement) {
        this.digit1.nativeElement.focus();
      }
    }, 0);
  }

  /**
   * Handle digit input and auto-focus to next input
   */
  onDigitInput(event: Event, currentInput: HTMLInputElement, nextInput: HTMLInputElement | null, prevInput: HTMLInputElement | null = null): void {
    // Ensure only one digit is in the input
    if (currentInput.value.length > 1) {
      currentInput.value = currentInput.value.slice(0, 1);
    }

    const value = currentInput.value;

    // Handle backspace key for KeyboardEvent
    if (event instanceof KeyboardEvent && event.key === 'Backspace') {
      if (value === '' && prevInput) {
        prevInput.focus();
        prevInput.select();
      }
      return;
    }

    // Only allow numbers
    if (!/^\d*$/.test(value)) {
      currentInput.value = '';
      return;
    }

    // Auto focus to next input when a digit is entered
    if (value.length === 1 && nextInput) {
      nextInput.focus();
    }

    // Combine all digits and update the form control
    this.updateFormValue();
  }

  /**
   * Handle paste event for the code
   */
  onPaste(event: ClipboardEvent): void {
    event.preventDefault();

    if (!event.clipboardData) {
      return;
    }

    const pastedText = event.clipboardData.getData('text');

    // Check if pasted text is a 6-digit number
    if (!/^\d{6}$/.test(pastedText)) {
      return;
    }

    // Find all digit inputs
    const inputs = document.querySelectorAll('.mfa-digit-input') as NodeListOf<HTMLInputElement>;

    // Fill each input with the corresponding digit
    for (let i = 0; i < Math.min(6, pastedText.length); i++) {
      if (inputs[i]) {
        inputs[i].value = pastedText[i];
      }
    }

    // Update the form value
    this.updateFormValue();

    // Focus on the last input
    if (inputs[5]) {
      inputs[5].focus();
    }
  }

  /**
   * Handle focus event to select the input content
   */
  onFocus(event: FocusEvent): void {
    const input = event.target as HTMLInputElement;
    if (input) {
      setTimeout(() => {
        input.select();
      }, 0);
    }
  }

  /**
   * Combine all digit inputs and update the form control
   */
  private updateFormValue(): void {
    const inputs = document.querySelectorAll('.mfa-digit-input') as NodeListOf<HTMLInputElement>;
    let combinedValue = '';

    inputs.forEach(input => {
      combinedValue += input.value || '';
    });

    this.mfaForm.get('code')?.setValue(combinedValue);
    this.mfaForm.get('code')?.markAsDirty();

    // If we have 6 digits, mark as touched to trigger validation
    if (combinedValue.length === 6) {
      this.mfaForm.get('code')?.markAsTouched();
    }
  }

  // Getter for easy access to form fields
  get code() {
    return this.mfaForm.get('code')!;
  }

  onSubmit(): void {
    // Reset error message
    this.mfaError = null;

    // Make sure we have the latest combined value
    this.updateFormValue();

    if (this.mfaForm.valid) {
      this.isLoading = true;

      // Prepare the MFA verification request
      const mfaData = {
        user_name: this.userName,
        password: this.password,
        login_first_factor: this.loginFirstFactor,
        code: this.code.value
      };

      // Call the MFA verification API
      this.http.post(`${this.configService.apiUrl}/access/mfa`, mfaData)
        .pipe(
          catchError(err => {
            console.error('MFA verification error:', err);
            this.mfaError = err.message || 'MFA verification failed. Please try again.';
            this.toastService.showToast(this.mfaError || 'MFA verification failed', NotifyType.ERROR);

            // Clear the inputs and focus on the first one
            const inputs = document.querySelectorAll('.mfa-digit-input') as NodeListOf<HTMLInputElement>;
            inputs.forEach(input => {
              input.value = '';
            });

            if (this.digit1 && this.digit1.nativeElement) {
              this.digit1.nativeElement.focus();
            }

            // Reset the form value
            this.mfaForm.get('code')?.setValue('');

            return throwError(() => err);
          }),
          finalize(() => {
            this.isLoading = false;
          })
        )
        .subscribe({
          next: (response: any) => {
            // Clear MFA data from the secure state service
            this.mfaStateService.clearMfaInfo();

            // Update user data in auth service
            this.authService.updateUser(response);

            this.toastService.showToast('MFA verification successful!', NotifyType.SUCCESS);
             window.location.href = this.returnUrl;
          },
          error: () => {
            // Error is already handled in the catchError operator
          }
        });
    } else {
      // Mark all form controls as touched to show validation errors
      Object.keys(this.mfaForm.controls).forEach(key => {
        const control = this.mfaForm.get(key);
        if (control) {
          control.markAsTouched();
        }
      });

      // Focus on the first empty input
      const inputs = document.querySelectorAll('.mfa-digit-input') as NodeListOf<HTMLInputElement>;
      for (let i = 0; i < inputs.length; i++) {
        if (!inputs[i].value) {
          inputs[i].focus();
          break;
        }
      }
    }
  }
}
