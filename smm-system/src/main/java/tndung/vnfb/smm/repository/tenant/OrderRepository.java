package tndung.vnfb.smm.repository.tenant;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import tndung.vnfb.smm.constant.enums.AddType;
import tndung.vnfb.smm.constant.enums.OrderStatus;
import tndung.vnfb.smm.entity.GOrder;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.Optional;

public interface OrderRepository extends TenantAwareRepository<GOrder, Long> {
    @Query("SELECT o FROM GOrder o WHERE o.id IN :orderIds AND o.tenantId = :#{T(tndung.vnfb.smm.config.TenantContext).getWildcardTenant()} AND o.isDeleted = false")
    List<GOrder> findByIdIn(@Param("orderIds") List<Long> orderIds);

    @Query("SELECT o FROM GOrder o WHERE o.id = :orderId AND o.tenantId = :#{T(tndung.vnfb.smm.config.TenantContext).getWildcardTenant()} AND o.isDeleted = false")
    Optional<GOrder> findById(@Param("orderId") Long orderId);


    Optional<GOrder> findByIdAndTenantIdAndIsDeletedFalse(Long orderId, String tenantId);
    @Query("SELECT o FROM GOrder o " +
            "WHERE o.status IN :statuses " +
            "AND o.isDeleted = false " +
            "AND o.service.addType != :addType")
    Page<GOrder> findOrderToReady(List<OrderStatus> statuses,
                                  AddType addType,
                                  Pageable pageable);

    @Query("SELECT COUNT(o) FROM GOrder o WHERE o.createdAt BETWEEN :startDate AND :endDate " +
            "AND o.tenantId = :tenantId AND o.isDeleted = false")
    Long countOrdersBetweenDates(@Param("startDate") OffsetDateTime startDate, @Param("endDate") OffsetDateTime endDate,

             @Param("tenantId") String tenantId);

    @Query("SELECT o FROM GOrder o " +
            "LEFT JOIN o.user u " +
            "LEFT JOIN o.service s " +
            "WHERE " +
            "(:keyword IS NULL OR " +
            "CAST(o.id AS string) LIKE CONCAT('%', LOWER(:keyword), '%') OR " +
            "LOWER(u.userName) LIKE CONCAT('%', LOWER(:keyword), '%') OR " +
            "LOWER(s.name) LIKE CONCAT('%', LOWER(:keyword), '%') OR " +
            "CAST(s.id AS string) LIKE CONCAT('%', LOWER(:keyword), '%')) " +
            "AND (:orderId IS NULL OR o.id = :orderId) " +
            "AND (CAST(:startDate AS timestamp) IS NULL OR o.createdAt >= :startDate) " +
            "AND (CAST(:endDate AS timestamp) IS NULL OR o.createdAt <= :endDate) " +
            "AND (:status IS NULL OR o.status = :status) " +
            "AND (:tenantId IS NULL OR o.tenantId = :tenantId) " +
            "AND o.isDeleted = false " +
            "ORDER BY o.createdAt DESC")
    Page<GOrder> searchAdminOrders(
            @Param("keyword") String keyword,
            @Param("orderId") Long orderId,
            @Param("startDate") OffsetDateTime startDate,
            @Param("endDate") OffsetDateTime endDate,
            @Param("status") OrderStatus status,
            @Param("tenantId") String tenantId,
            Pageable pageable);

    @Query("SELECT o FROM GOrder o WHERE " +
            "(:link IS NULL OR LOWER(o.link) LIKE LOWER(CONCAT('%', :link, '%'))) " +
            "AND (:orderId IS NULL OR o.id = :orderId) " +
            "AND (:apiProviderId IS NULL OR o.apiProvider.id = :apiProviderId) " +
            "AND (:categoryId IS NULL OR o.service.category.id = :categoryId) " +
            "AND (:serviceId IS NULL OR o.service.id = :serviceId) " +
            "AND (CAST(:startDate AS timestamp)  IS NULL OR o.createdAt >= :startDate) " +
            "AND (CAST(:endDate AS timestamp) IS NULL OR o.createdAt <= :endDate) " +
            "AND (:status IS NULL OR o.status = :status)" +
            "AND (:userId IS NULL OR o.user.id = :userId)" +
            " AND o.tenantId = :#{T(tndung.vnfb.smm.config.TenantContext).getWildcardTenant()} "+
            " AND o.isDeleted = false" +
            " order by o.createdAt desc")
    Page<GOrder> searchOrders(
            @Param("link") String link,
            @Param("orderId") Long orderId,
            @Param("apiProviderId") Long apiProviderId,
            @Param("categoryId") Long categoryId,
            @Param("serviceId") Long serviceId,
            @Param("startDate") OffsetDateTime startDate,
            @Param("endDate") OffsetDateTime endDate,
            @Param("status") OrderStatus status,
            @Param("userId") Long userId,
            Pageable pageable);

    @Query("SELECT o FROM GOrder o WHERE " +
            " o.user.id = :userId " +
            "AND (:link IS NULL OR o.link = :link) " +
            "AND (:orderId IS NULL OR o.id = :orderId) " +
            "AND (:categoryId IS NULL OR o.service.category.id = :categoryId) " +
            "AND (:serviceId IS NULL OR o.service.id = :serviceId) " +
            "AND (CAST(:startDate AS timestamp) IS NULL OR o.createdAt >= :startDate) " +
            "AND (CAST(:endDate AS timestamp) IS NULL OR o.createdAt <= :endDate)" +
            "AND (:status IS NULL OR o.status = :status)" +
            " AND o.tenantId = :#{T(tndung.vnfb.smm.config.TenantContext).getWildcardTenant()} "+
            " AND o.isDeleted = false" +
            " order by o.createdAt desc")
    Page<GOrder> searchMyOrders(
            @Param("userId") Long userId,
            @Param("link") String link,
            @Param("orderId") Long orderId,
            @Param("serviceId") Long serviceId,
            @Param("categoryId") Long categoryId,

            @Param("startDate") OffsetDateTime startDate,
            @Param("endDate") OffsetDateTime endDate,
            @Param("status") OrderStatus status,
            Pageable pageable);
    /**
     * Count orders for a specific tenant ID
     */
    @Query("SELECT COUNT(o) FROM GOrder o WHERE o.tenantId = :tenantId AND o.isDeleted = false")
    Long countByTenantId(@Param("tenantId") String tenantId);

    // Dashboard V2 queries
    @Query(value = "SELECT COALESCE(SUM(o.charge), 0) FROM g_order o WHERE o.created_at BETWEEN :startDate AND :endDate " +
            "AND o.tenant_id = :#{T(tndung.vnfb.smm.config.TenantContext).getWildcardTenant()} AND o.is_deleted = false", nativeQuery = true)
    BigDecimal sumOrderValueBetweenDates(@Param("startDate") OffsetDateTime startDate, @Param("endDate") OffsetDateTime endDate);

    @Query(value = "SELECT o.created_at as date, COUNT(o) as orderCount, COALESCE(SUM(o.charge), 0) as orderValue " +
            "FROM g_order o WHERE o.created_at BETWEEN :startDate AND :endDate " +
            "AND o.tenant_id = :tenantId AND o.is_deleted = false " +
            "GROUP BY o.created_at ORDER BY o.created_at", nativeQuery = true)
    List<Object[]> getDailyOrderStats(@Param("startDate") OffsetDateTime startDate, @Param("endDate") OffsetDateTime endDate,  @Param("tenantId") String tenantId);

    @Query(value = "SELECT s.id as serviceId, s.name as serviceName, COUNT(o) as totalOrders, " +
            "COALESCE(SUM(o.charge), 0) as totalRevenue, c.name as category, " +
            "COALESCE(p.name, 'Unknown') as platform ,s.price as servicePrice " +
            "FROM g_order o " +
            "JOIN g_service s ON o.service_id = s.id " +
            "JOIN category c ON s.category_id = c.id " +
            "LEFT JOIN platform p ON c.platform_id = p.id " +
            "WHERE o.created_at BETWEEN :startDate AND :endDate " +
            "AND o.tenant_id = :tenantId" +
            " AND o.is_deleted = false AND s.is_deleted = false " +
            "GROUP BY s.id, s.name, c.name, p.name " +
            "ORDER BY COUNT(o) DESC", nativeQuery = true)
    List<Object[]> getTopServiceStats(@Param("startDate") OffsetDateTime startDate, @Param("endDate") OffsetDateTime endDate
                                ,      @Param("tenantId") String tenantId  );

    @Query(value = "SELECT Do.created_at as date, COUNT(o) as orderCount, COALESCE(SUM(o.charge), 0) as orderValue " +
            "FROM g_order o WHERE o.created_at BETWEEN :startDate AND :endDate " +
            "AND o.service_id = :serviceId " +
            "AND o.tenant_id = :tenantId AND o.is_deleted = false " +
            "GROUP BY o.created_at ORDER BY o.created_at", nativeQuery = true)
    List<Object[]> getDailyOrderStatsByService(@Param("startDate") OffsetDateTime startDate,
                                              @Param("endDate") OffsetDateTime endDate,
                                              @Param("serviceId") Long serviceId
            , @Param("tenantId") String tenantId);

    @Query(value = "SELECT s.id as serviceId, s.name as serviceName, COUNT(o) as totalOrders, " +
            "COALESCE(SUM(o.charge), 0) as totalRevenue, c.name as category, " +
            "COALESCE(p.name, 'Unknown') as platform , s.price as servicePrice" +
            "FROM g_order o " +
            "JOIN g_service s ON o.service_id = s.id " +
            "JOIN category c ON s.category_id = c.id " +
            "LEFT JOIN platform p ON c.platform_id = p.id " +
            "WHERE o.created_at BETWEEN :startDate AND :endDate " +
            "AND o.service_id = :serviceId " +
            "AND o.tenant_id = :tenantId AND o.is_deleted = false " +
            "GROUP BY s.id, s.name, c.name, p.name", nativeQuery = true)
    List<Object[]> getServiceStatsByServiceId(@Param("startDate") OffsetDateTime startDate,
                                             @Param("endDate") OffsetDateTime endDate,
                                             @Param("serviceId") Long serviceId, @Param("tenantId") String tenantId);

    @Query(value = "SELECT COUNT(o) FROM g_order o WHERE o.service_id = :serviceId " +
            "AND (CAST(:startDate AS timestamp) IS NULL OR o.created_at >= CAST(:startDate AS timestamp)) " +
            "AND (CAST(:endDate AS timestamp) IS NULL OR o.created_at <= CAST(:endDate AS timestamp)) " +
            "AND o.tenant_id = :tenantId AND o.is_deleted = false",
            nativeQuery = true)
    Long countOrdersByService(@Param("serviceId") Long serviceId,
                              @Param("startDate") OffsetDateTime startDate,
                              @Param("endDate") OffsetDateTime endDate, @Param("tenantId") String tenantId);




    // Cách 1: Không dùng countQuery, để JPA tự generate
    @Query("SELECT o FROM GOrder o WHERE o.service.id = :serviceId " +
            "AND o.createdAt BETWEEN :startDate AND :endDate "+
            "AND o.tenantId = :tenantId " +
            "AND o.isDeleted = false " +
            "ORDER BY o.createdAt DESC")
    Page<GOrder> findOrdersByService(@Param("serviceId") Long serviceId,
                                        @Param("startDate") OffsetDateTime startDate,
                                        @Param("endDate") OffsetDateTime endDate,
                                     @Param("tenantId") String tenantId,
                                        Pageable pageable);

    @Query(value = "SELECT COALESCE(SUM(o.charge), 0) FROM g_order o WHERE o.service_id = :serviceId " +
            "AND (CAST(:startDate AS timestamp) IS NULL OR o.created_at >= CAST(:startDate AS timestamp)) " +
            "AND (CAST(:endDate AS timestamp) IS NULL OR o.created_at <= CAST(:endDate AS timestamp)) " +
            "AND o.tenant_id = :tenantId AND o.is_deleted = false",
            nativeQuery = true)
    BigDecimal sumRevenueByService(@Param("serviceId") Long serviceId,
                                   @Param("startDate") OffsetDateTime startDate,
                                   @Param("endDate") OffsetDateTime endDate,
                                   @Param("tenantId") String tenantId);

    @Query(value = "SELECT COUNT(DISTINCT o.id) FROM g_order o " +
            "INNER JOIN g_transaction t ON o.id = t.order_id " +
            "WHERE o.service_id = :serviceId AND t.type IN ('Partial', 'Refund') " +
            "AND (CAST(:startDate AS timestamp) IS NULL OR o.created_at >= CAST(:startDate AS timestamp)) " +
            "AND (CAST(:endDate AS timestamp) IS NULL OR o.created_at <= CAST(:endDate AS timestamp)) " +
            "AND o.tenant_id = :tenantId AND o.is_deleted = false",
            nativeQuery = true)
    Long countRefundedOrdersByService(@Param("serviceId") Long serviceId,
                                      @Param("startDate") OffsetDateTime startDate,
                                      @Param("endDate") OffsetDateTime endDate,
                                      @Param("tenantId") String tenantId);

    @Query(value = "SELECT COALESCE(SUM(t.change), 0) FROM g_order o " +
            "INNER JOIN g_transaction t ON o.id = t.order_id " +
            "WHERE o.service_id = :serviceId AND t.type IN ('Partial', 'Refund') " +
            "AND (CAST(:startDate AS timestamp) IS NULL OR o.created_at >= CAST(:startDate AS timestamp)) " +
            "AND (CAST(:endDate AS timestamp) IS NULL OR o.created_at <= CAST(:endDate AS timestamp)) " +
            "AND o.tenant_id = :tenantId AND o.is_deleted = false",
            nativeQuery = true)
    BigDecimal sumRefundedAmountByService(@Param("serviceId") Long serviceId,
                                          @Param("startDate") OffsetDateTime startDate,
                                          @Param("endDate") OffsetDateTime endDate,
                                          @Param("tenantId") String tenantId);

    @Query(value = "SELECT COALESCE(SUM(o.quantity), 0) FROM g_order o WHERE o.service_id = :serviceId " +
            "AND (CAST(:startDate AS timestamp) IS NULL OR o.created_at >= CAST(:startDate AS timestamp)) " +
            "AND (CAST(:endDate AS timestamp) IS NULL OR o.created_at <= CAST(:endDate AS timestamp)) " +
            "AND o.tenant_id = :tenantId AND o.is_deleted = false",
            nativeQuery = true)
    Long sumQuantityByService(@Param("serviceId") Long serviceId,
                              @Param("startDate") OffsetDateTime startDate,
                              @Param("endDate") OffsetDateTime endDate,
                              @Param("tenantId") String tenantId);
    @Query(value = "SELECT o.created_at as date, COALESCE(SUM(o.charge), 0) as value " +
            "FROM g_order o WHERE o.created_at BETWEEN :startDate AND :endDate " +
            "AND (:serviceId IS NULL OR o.service_id = :serviceId) " +
            "AND o.tenant_id = :tenantId AND o.is_deleted = false " +
            "GROUP BY o.created_at ORDER BY o.created_at", nativeQuery = true)
    List<Object[]> getDailyRevenueByService(@Param("startDate") OffsetDateTime startDate,
                                           @Param("endDate") OffsetDateTime endDate,
                                           @Param("serviceId") Long serviceId,
                                            @Param("tenantId") String tenantId);

    /**
     * Find top N orders by service ID and tenant ID ordered by created date descending
     * Used for average time calculation
     */
    @Query(value = "SELECT * FROM g_order o WHERE o.service_id = :serviceId " +
            "AND o.tenant_id = :tenantId " +
            "AND o.is_deleted = false " +
            "ORDER BY o.created_at DESC " +
            "LIMIT :limit", nativeQuery = true)
    List<GOrder> findTopNByServiceIdAndTenantIdOrderByCreatedAtDesc(
            @Param("serviceId") Long serviceId, 
            @Param("tenantId") String tenantId, 
            @Param("limit") int limit);

    /**
     * Find orders by service ID and tenant ID with specific statuses for average time calculation
     */
    @Query("SELECT o FROM GOrder o WHERE o.service.id = :serviceId " +
            "AND o.tenantId = :tenantId " +
            "AND o.status IN :statuses " +
            "AND o.isDeleted = false " +
            "ORDER BY o.createdAt DESC")
    List<GOrder> findByServiceIdAndTenantIdAndStatusInOrderByCreatedAtDesc(
            @Param("serviceId") Long serviceId,
            @Param("tenantId") String tenantId,
            @Param("statuses") List<OrderStatus> statuses,
            Pageable pageable);

    /**
     * Find completed orders by service ID and tenant ID for average time calculation
     */
    @Query("SELECT o FROM GOrder o WHERE o.service.id = :serviceId " +
            "AND o.tenantId = :tenantId " +
            "AND o.status = 'COMPLETED' " +
            "AND o.isDeleted = false " +
            "ORDER BY o.createdAt DESC")
    List<GOrder> findCompletedOrdersByServiceIdAndTenantIdOrderByCreatedAtDesc(
            @Param("serviceId") Long serviceId,
            @Param("tenantId") String tenantId,
            Pageable pageable);

    /**
     * Find orders with specific quantity by service ID and tenant ID
     */
    @Query("SELECT o FROM GOrder o WHERE o.service.id = :serviceId " +
            "AND o.tenantId = :tenantId " +
            "AND o.quantity = :quantity " +
            "AND o.status = 'COMPLETED' " +
            "AND o.isDeleted = false " +
            "ORDER BY o.createdAt DESC")
    List<GOrder> findByServiceIdAndTenantIdAndQuantityAndStatusOrderByCreatedAtDesc(
            @Param("serviceId") Long serviceId,
            @Param("tenantId") String tenantId,
            @Param("quantity") Integer quantity,
            Pageable pageable);

    /**
     * Find latest orders for average time calculation
     */
    @Query("SELECT o FROM GOrder o WHERE o.service.id = :serviceId " +
            "AND o.tenantId = :#{T(tndung.vnfb.smm.config.TenantContext).getWildcardTenant()} " +
            "AND o.isDeleted = false " +
            "ORDER BY o.createdAt DESC")
    List<GOrder> findLatestOrdersForAverageTimeCalculation(
            @Param("serviceId") Long serviceId,
            Pageable pageable);

    /**
     * Find latest orders for average time calculation with limit
     */
    default List<GOrder> findLatestOrdersForAverageTimeCalculation(Long serviceId, int limit) {
        return findLatestOrdersForAverageTimeCalculation(serviceId,
            org.springframework.data.domain.PageRequest.of(0, limit));
    }
}
