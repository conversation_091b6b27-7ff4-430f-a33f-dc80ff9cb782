import { Component, OnInit, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { SuperPlatformRes } from '../../model/response/super-platform.model';
import { IconName } from '@fortawesome/fontawesome-svg-core';
import { IconsModule } from '../../icons/icons.module';
import { SocialIconComponent } from '../common/social-icon/social-icon.component';
import { AddPlatformLightComponent } from '../popup/add-platform-light/add-platform-light.component';
import { CdkDragDrop, moveItemInArray, CdkDrag, CdkDropList, CdkDragHandle, CdkDragPreview, CdkDragStart } from '@angular/cdk/drag-drop';
import { AdminServiceService } from '../../core/services/admin-service.service';

@Component({
  selector: 'app-platform-management',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    IconsModule,
    SocialIconComponent,
    AddPlatformLightComponent,
    CdkDrag,
    CdkDropList,
    CdkDragHandle,
    CdkDragPreview
  ],
  templateUrl: './platform-management.component.html',
  styleUrl: './platform-management.component.css'
})
export class PlatformManagementComponent implements OnInit {
  @Output() close = new EventEmitter<void>();
  @Output() platformsUpdated = new EventEmitter<SuperPlatformRes[]>();

  platforms: SuperPlatformRes[] = [];
  loading = false;
  loadingDelete = false;
  showAddPlatformModal = false;
  showEditPlatformModal = false;
  showDeleteConfirmation = false;
  platformToDelete: SuperPlatformRes | null = null;
  platformToEdit: SuperPlatformRes | null = null;

  constructor(private categoriesService: AdminServiceService) {}

  ngOnInit(): void {
    this.loadPlatforms();
  }

  loadPlatforms(): void {
    this.loading = true;
    this.categoriesService.getPlatformsWithServices().subscribe({
      next: (platforms) => {
        // Filter out hidden platforms and platforms with ID = 1, then convert icon strings to IconName type
        this.platforms = platforms
          .filter(platform => !platform.hide  && platform.name !== 'No network')
          .map(platform => ({
            ...platform,
            icon: platform.icon as IconName // Convert string to IconName
          }));
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading platforms:', error);
        this.loading = false;
      }
    });
  }

  openAddPlatformModal(): void {
    this.showAddPlatformModal = true;
  }

  closeAddPlatformModal(): void {
    this.showAddPlatformModal = false;
  }

  onPlatformAdded(platform: any): void {
    // Reload platforms after adding a new one
    this.loadPlatforms();
  }

  onEdit(platform: SuperPlatformRes): void {
    // Set the platform to edit
    this.platformToEdit = platform;
    // Show the edit platform modal
    this.showEditPlatformModal = true;
  }

  closeEditPlatformModal(): void {
    this.showEditPlatformModal = false;
    this.platformToEdit = null;
  }

  onPlatformUpdated(platform: any): void {
    console.log('Platform updated:', platform);
    // Reload platforms after updating
    this.loadPlatforms();
  }

  onDelete(platform: SuperPlatformRes): void {
    // Set the platform to delete and show confirmation
    this.platformToDelete = platform;
    this.showDeleteConfirmation = true;
  }

  confirmDelete(): void {
    if (!this.platformToDelete) return;

    this.loadingDelete = true;
    this.categoriesService.deletePlatform(this.platformToDelete.id).subscribe({
      next: () => {
        console.log(`Platform ${this.platformToDelete?.name} deleted successfully`);
        this.loadPlatforms();
        this.closeDeleteConfirmation();
      },
      error: (error) => {
        console.error('Error deleting platform:', error);
        this.loadingDelete = false;
      }
    });
  }

  closeDeleteConfirmation(): void {
    this.showDeleteConfirmation = false;
    this.platformToDelete = null;
    this.loadingDelete = false;
  }

  onDrop(event: CdkDragDrop<SuperPlatformRes[]>): void {
    if (event.previousIndex === event.currentIndex) {
      return; // No change in position
    }

    // Get the platforms being swapped
    const platform1 = this.platforms[event.previousIndex];
    const platform2 = this.platforms[event.currentIndex];

    // Update the local array
    moveItemInArray(this.platforms, event.previousIndex, event.currentIndex);

    // Call the API to update the order in the backend
    this.categoriesService.swapPlatformPositions(platform1.id, platform2.id).subscribe({
      next: () => {
        console.log('Platform positions reordered successfully');
        // Emit the updated platforms
        this.platformsUpdated.emit(this.platforms);
      },
      error: (error) => {
        console.error('Error reordering platform positions:', error);
        // Revert the local change if the API call fails
        moveItemInArray(this.platforms, event.currentIndex, event.previousIndex);
      }
    });
  }

  onClose(): void {
    this.close.emit();
  }

  onOverlayClick(event: MouseEvent): void {
    // Close if clicking on the overlay background, not the modal content
    if (event.target === event.currentTarget) {
      this.onClose();
    }
  }

  onDragStarted(event: CdkDragStart): void {
    // Get the width of the original element
    const element = event.source.element.nativeElement;
    const width = element.offsetWidth;

    // Set the CSS variable for the drag preview width
    document.documentElement.style.setProperty('--item-width', `${width}px`);
  }
}
