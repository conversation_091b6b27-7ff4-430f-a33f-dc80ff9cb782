package tndung.vnfb.smm.service;


import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import tndung.vnfb.smm.constant.enums.Role;
import tndung.vnfb.smm.dto.request.*;
import tndung.vnfb.smm.dto.response.*;
import tndung.vnfb.smm.dto.response.smm.SMMBalanceRes;
import tndung.vnfb.smm.entity.GOrder;
import tndung.vnfb.smm.entity.GUser;
import tndung.vnfb.smm.entity.PaymentMethod;

import java.util.List;
import java.util.Optional;

public interface GUserService {

    GUserRes deposit(Long userId, DepositReq req);
    GUser depositProcess(Long userId, DepositReq req, PaymentMethod paymentMethod);
    LanguageRes getLanguage();

    String getUserLanguageFromCache(Long userId);

    LanguageRes setLanguage(LanguageReq lang);

    GUserRes setCurrency(CurrencyReq req);

    GUserRes setTimezone(TimezoneReq req);
    void refund(GOrder order);
    SMMBalanceRes getBalance();

    GUserRes deactivate(Long id);

    GUserRes active(Long id);

    GUserPasswordRes resetPassword(Long id);

    ApiKeyUserRes generateApiKey();

    void changePassword(UserChangePassReq req);

    void sendEmailVerification();

    void confirmEmailVerification(String token);

    GUserRes edit(EditUserReq req);

    GUserRes create(UserReq req, List<Role> roles);

    GUser findByEmail(String email);

    GUser findById(Long id);

    TransactionRes addFund(Long id, AddFundReq addFundReq);

    Optional<GUser> findByApiKey(String apiKey);

    GUserRes getInfo();

    ApiKeyUserRes getApiKey();

    Page<GUserSuperRes> search(UserSearchReq req, Pageable pageable);

    GUserSuperRes getDetailUser(Long id);

    Page<TransactionRes> getTransactionsByUser(Long id, TransactionSearchReq req, Pageable pageable);

    Page<MyTransactionRes> getMyTransactions(TransactionSearchReq req, Pageable pageable);

    GUserSuperRes superEdit(Long id, EditUserReq userReq);

    GUserSuperRes addAllCustomDiscount(Long id, CustomDiscountReq customDiscount);

    GUserSuperRes customReferral(Long id, CustomReferralReq req);

    SpecialPriceRes addCustomDiscount(Long id, CustomDiscountServiceReq customDiscount);

    List<SpecialPriceRes> getCustomDiscountByUser(Long userId);

    List<SpecialPriceRes> getCustomDiscountByService(Long serviceId);

    List<SpecialPriceCountRes> getCountCustomDiscountByService();

    SpecialPriceRes editCustomDiscount( Long id, CustomDiscountServiceReq customDiscount);

    void deleteCustomDiscount(Long id);

    void deleteCustomDiscountByUser(Long userId);

    void deleteCustomDiscountByService(Long serviceId);

    MySummaryTransaction summaryMyTransactions();

    SummaryTransaction summaryTransactions(Long userId);
}
